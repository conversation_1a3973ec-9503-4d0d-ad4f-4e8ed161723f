!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).markdownit=t()}(this,function(){let T={};function n(e,t){let r=function(t){var r=T[t];if(!r){r=T[t]=[];for(let t=0;t<128;t++){let e=String.fromCharCode(t);r.push(e)}for(let e=0;e<t.length;e++){var n=t.charCodeAt(e);r[n]="%"+("0"+n.toString(16).toUpperCase()).slice(-2)}}return r}(t="string"!=typeof t?n.defaultChars:t);return e.replace(/(%[a-f0-9]{2})+/gi,function(i){let s="";for(let n=0,e=i.length;n<e;n+=3){var o=parseInt(i.slice(n+1,n+3),16);if(o<128)s+=r[o];else{if(192==(224&o)&&n+3<e){var u=parseInt(i.slice(n+4,n+6),16);if(128==(192&u)){let e=o<<6&1984|63&u;s+=e<128?"��":String.fromCharCode(e),n+=3;continue}}if(224==(240&o)&&n+6<e){let t=parseInt(i.slice(n+4,n+6),16),r=parseInt(i.slice(n+7,n+9),16);if(128==(192&t)&&128==(192&r)){let e=o<<12&61440|t<<6&4032|63&r;s+=e<2048||55296<=e&&e<=57343?"���":String.fromCharCode(e),n+=6;continue}}if(240==(248&o)&&n+9<e){let e=parseInt(i.slice(n+4,n+6),16),t=parseInt(i.slice(n+7,n+9),16),r=parseInt(i.slice(n+10,n+12),16);if(128==(192&e)&&128==(192&t)&&128==(192&r)){u=o<<18&1835008|e<<12&258048|t<<6&4032|63&r;u<65536||1114111<u?s+="����":(u-=65536,s+=String.fromCharCode(55296+(u>>10),56320+(1023&u))),n+=9;continue}}s+="�"}}return s})}n.defaultChars=";/?:@&=+$,#",n.componentChars="";let R={};function r(n,e,i){"string"!=typeof e&&(i=e,e=r.defaultChars),void 0===i&&(i=!0);var s=function(t){var r=R[t];if(!r){r=R[t]=[];for(let t=0;t<128;t++){let e=String.fromCharCode(t);/^[0-9a-z]$/i.test(e)?r.push(e):r.push("%"+("0"+t.toString(16).toUpperCase()).slice(-2))}for(let e=0;e<t.length;e++)r[t.charCodeAt(e)]=t[e]}return r}(e);let o="";for(let t=0,r=n.length;t<r;t++){let e=n.charCodeAt(t);if(i&&37===e&&t+2<r&&/^[0-9a-f]{2}$/i.test(n.slice(t+1,t+3)))o+=n.slice(t,t+3),t+=2;else if(e<128)o+=s[e];else if(55296<=e&&e<=57343){if(55296<=e&&e<=56319&&t+1<r){let e=n.charCodeAt(t+1);if(56320<=e&&e<=57343){o+=encodeURIComponent(n[t]+n[t+1]),t++;continue}}o+="%EF%BF%BD"}else o+=encodeURIComponent(n[t])}return o}function i(e){let t="";return t=(t=(t+=e.protocol||"")+(e.slashes?"//":""))+(e.auth?e.auth+"@":""),e.hostname&&-1!==e.hostname.indexOf(":")?t+="["+e.hostname+"]":t+=e.hostname||"",t=(t=(t=(t+=e.port?":"+e.port:"")+(e.pathname||""))+(e.search||""))+(e.hash||"")}function s(){this.protocol=null,this.slashes=null,this.auth=null,this.port=null,this.hostname=null,this.hash=null,this.search=null,this.pathname=null}r.defaultChars=";/?:@&=+$,-_.!~*'()#",r.componentChars="-_.!~*'()";let N=/^([a-z0-9.+-]+:)/i,P=/:[0-9]*$/,O=/^(\/\/?(?!\/)[^\?\s]*)(\?[^\s]*)?$/,j=["{","}","|","\\","^","`"].concat(["<",">",'"',"`"," ","\r","\n","\t"]),Z=["'"].concat(j),$=["%","/","?",";","#"].concat(Z),U=["/","?","#"],H=/^[+a-z0-9A-Z_-]{0,63}$/,V=/^([+a-z0-9A-Z_-]{0,63})(.*)$/,G={javascript:!0,"javascript:":!0},W={http:!0,https:!0,ftp:!0,gopher:!0,file:!0,"http:":!0,"https:":!0,"ftp:":!0,"gopher:":!0,"file:":!0};function J(e,t){var r;return e&&e instanceof s?e:((r=new s).parse(e,t),r)}s.prototype.parse=function(e,t){let r,s,n,o=e;if(o=o.trim(),!t&&1===e.split("#").length){let e=O.exec(o);if(e)return this.pathname=e[1],e[2]&&(this.search=e[2]),this}let i=N.exec(o);if(i&&(i=i[0],r=i.toLowerCase(),this.protocol=i,o=o.substr(i.length)),(t||i||o.match(/^\/\/[^@\/]+@[^@\/]+/))&&(!(n="//"===o.substr(0,2))||i&&G[i]||(o=o.substr(2),this.slashes=!0)),!G[i]&&(n||i&&!W[i])){let e,t,r=-1;for(let e=0;e<U.length;e++)-1!==(s=o.indexOf(U[e]))&&(-1===r||s<r)&&(r=s);-1!==(t=-1===r?o.lastIndexOf("@"):o.lastIndexOf("@",r))&&(e=o.slice(0,t),o=o.slice(t+1),this.auth=e),r=-1;for(let e=0;e<$.length;e++)-1!==(s=o.indexOf($[e]))&&(-1===r||s<r)&&(r=s);-1===r&&(r=o.length),":"===o[r-1]&&r--;let n=o.slice(0,r),i=(o=o.slice(r),this.parseHost(n),this.hostname=this.hostname||"","["===this.hostname[0]&&"]"===this.hostname[this.hostname.length-1]);if(!i){let s=this.hostname.split(/\./);for(let i=0,e=s.length;i<e;i++){let n=s[i];if(n&&!n.match(H)){let r="";for(let e=0,t=n.length;e<t;e++)127<n.charCodeAt(e)?r+="x":r+=n[e];if(!r.match(H)){let e=s.slice(0,i),t=s.slice(i+1),r=n.match(V);r&&(e.push(r[1]),t.unshift(r[2])),t.length&&(o=t.join(".")+o),this.hostname=e.join(".");break}}}}255<this.hostname.length&&(this.hostname=""),i&&(this.hostname=this.hostname.substr(1,this.hostname.length-2))}e=o.indexOf("#"),-1!==e&&(this.hash=o.substr(e),o=o.slice(0,e)),t=o.indexOf("?");return-1!==t&&(this.search=o.substr(t),o=o.slice(0,t)),o&&(this.pathname=o),W[r]&&this.hostname&&!this.pathname&&(this.pathname=""),this},s.prototype.parseHost=function(e){var t=P.exec(e);t&&(":"!==(t=t[0])&&(this.port=t.substr(1)),e=e.substr(0,e.length-t.length)),e&&(this.hostname=e)};var u,a,o,l,Q=Object.freeze({__proto__:null,decode:n,encode:r,format:i,parse:J}),X=/[\0-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/,Y=/[\0-\x1F\x7F-\x9F]/,K=/[!-#%-\*,-\/:;\?@\[-\]_\{\}\xA1\xA7\xAB\xB6\xB7\xBB\xBF\u037E\u0387\u055A-\u055F\u0589\u058A\u05BE\u05C0\u05C3\u05C6\u05F3\u05F4\u0609\u060A\u060C\u060D\u061B\u061D-\u061F\u066A-\u066D\u06D4\u0700-\u070D\u07F7-\u07F9\u0830-\u083E\u085E\u0964\u0965\u0970\u09FD\u0A76\u0AF0\u0C77\u0C84\u0DF4\u0E4F\u0E5A\u0E5B\u0F04-\u0F12\u0F14\u0F3A-\u0F3D\u0F85\u0FD0-\u0FD4\u0FD9\u0FDA\u104A-\u104F\u10FB\u1360-\u1368\u1400\u166E\u169B\u169C\u16EB-\u16ED\u1735\u1736\u17D4-\u17D6\u17D8-\u17DA\u1800-\u180A\u1944\u1945\u1A1E\u1A1F\u1AA0-\u1AA6\u1AA8-\u1AAD\u1B5A-\u1B60\u1B7D\u1B7E\u1BFC-\u1BFF\u1C3B-\u1C3F\u1C7E\u1C7F\u1CC0-\u1CC7\u1CD3\u2010-\u2027\u2030-\u2043\u2045-\u2051\u2053-\u205E\u207D\u207E\u208D\u208E\u2308-\u230B\u2329\u232A\u2768-\u2775\u27C5\u27C6\u27E6-\u27EF\u2983-\u2998\u29D8-\u29DB\u29FC\u29FD\u2CF9-\u2CFC\u2CFE\u2CFF\u2D70\u2E00-\u2E2E\u2E30-\u2E4F\u2E52-\u2E5D\u3001-\u3003\u3008-\u3011\u3014-\u301F\u3030\u303D\u30A0\u30FB\uA4FE\uA4FF\uA60D-\uA60F\uA673\uA67E\uA6F2-\uA6F7\uA874-\uA877\uA8CE\uA8CF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA95F\uA9C1-\uA9CD\uA9DE\uA9DF\uAA5C-\uAA5F\uAADE\uAADF\uAAF0\uAAF1\uABEB\uFD3E\uFD3F\uFE10-\uFE19\uFE30-\uFE52\uFE54-\uFE61\uFE63\uFE68\uFE6A\uFE6B\uFF01-\uFF03\uFF05-\uFF0A\uFF0C-\uFF0F\uFF1A\uFF1B\uFF1F\uFF20\uFF3B-\uFF3D\uFF3F\uFF5B\uFF5D\uFF5F-\uFF65]|\uD800[\uDD00-\uDD02\uDF9F\uDFD0]|\uD801\uDD6F|\uD802[\uDC57\uDD1F\uDD3F\uDE50-\uDE58\uDE7F\uDEF0-\uDEF6\uDF39-\uDF3F\uDF99-\uDF9C]|\uD803[\uDEAD\uDF55-\uDF59\uDF86-\uDF89]|\uD804[\uDC47-\uDC4D\uDCBB\uDCBC\uDCBE-\uDCC1\uDD40-\uDD43\uDD74\uDD75\uDDC5-\uDDC8\uDDCD\uDDDB\uDDDD-\uDDDF\uDE38-\uDE3D\uDEA9]|\uD805[\uDC4B-\uDC4F\uDC5A\uDC5B\uDC5D\uDCC6\uDDC1-\uDDD7\uDE41-\uDE43\uDE60-\uDE6C\uDEB9\uDF3C-\uDF3E]|\uD806[\uDC3B\uDD44-\uDD46\uDDE2\uDE3F-\uDE46\uDE9A-\uDE9C\uDE9E-\uDEA2\uDF00-\uDF09]|\uD807[\uDC41-\uDC45\uDC70\uDC71\uDEF7\uDEF8\uDF43-\uDF4F\uDFFF]|\uD809[\uDC70-\uDC74]|\uD80B[\uDFF1\uDFF2]|\uD81A[\uDE6E\uDE6F\uDEF5\uDF37-\uDF3B\uDF44]|\uD81B[\uDE97-\uDE9A\uDFE2]|\uD82F\uDC9F|\uD836[\uDE87-\uDE8B]|\uD83A[\uDD5E\uDD5F]/,ee=/[\$\+<->\^`\|~\xA2-\xA6\xA8\xA9\xAC\xAE-\xB1\xB4\xB8\xD7\xF7\u02C2-\u02C5\u02D2-\u02DF\u02E5-\u02EB\u02ED\u02EF-\u02FF\u0375\u0384\u0385\u03F6\u0482\u058D-\u058F\u0606-\u0608\u060B\u060E\u060F\u06DE\u06E9\u06FD\u06FE\u07F6\u07FE\u07FF\u0888\u09F2\u09F3\u09FA\u09FB\u0AF1\u0B70\u0BF3-\u0BFA\u0C7F\u0D4F\u0D79\u0E3F\u0F01-\u0F03\u0F13\u0F15-\u0F17\u0F1A-\u0F1F\u0F34\u0F36\u0F38\u0FBE-\u0FC5\u0FC7-\u0FCC\u0FCE\u0FCF\u0FD5-\u0FD8\u109E\u109F\u1390-\u1399\u166D\u17DB\u1940\u19DE-\u19FF\u1B61-\u1B6A\u1B74-\u1B7C\u1FBD\u1FBF-\u1FC1\u1FCD-\u1FCF\u1FDD-\u1FDF\u1FED-\u1FEF\u1FFD\u1FFE\u2044\u2052\u207A-\u207C\u208A-\u208C\u20A0-\u20C0\u2100\u2101\u2103-\u2106\u2108\u2109\u2114\u2116-\u2118\u211E-\u2123\u2125\u2127\u2129\u212E\u213A\u213B\u2140-\u2144\u214A-\u214D\u214F\u218A\u218B\u2190-\u2307\u230C-\u2328\u232B-\u2426\u2440-\u244A\u249C-\u24E9\u2500-\u2767\u2794-\u27C4\u27C7-\u27E5\u27F0-\u2982\u2999-\u29D7\u29DC-\u29FB\u29FE-\u2B73\u2B76-\u2B95\u2B97-\u2BFF\u2CE5-\u2CEA\u2E50\u2E51\u2E80-\u2E99\u2E9B-\u2EF3\u2F00-\u2FD5\u2FF0-\u2FFF\u3004\u3012\u3013\u3020\u3036\u3037\u303E\u303F\u309B\u309C\u3190\u3191\u3196-\u319F\u31C0-\u31E3\u31EF\u3200-\u321E\u322A-\u3247\u3250\u3260-\u327F\u328A-\u32B0\u32C0-\u33FF\u4DC0-\u4DFF\uA490-\uA4C6\uA700-\uA716\uA720\uA721\uA789\uA78A\uA828-\uA82B\uA836-\uA839\uAA77-\uAA79\uAB5B\uAB6A\uAB6B\uFB29\uFBB2-\uFBC2\uFD40-\uFD4F\uFDCF\uFDFC-\uFDFF\uFE62\uFE64-\uFE66\uFE69\uFF04\uFF0B\uFF1C-\uFF1E\uFF3E\uFF40\uFF5C\uFF5E\uFFE0-\uFFE6\uFFE8-\uFFEE\uFFFC\uFFFD]|\uD800[\uDD37-\uDD3F\uDD79-\uDD89\uDD8C-\uDD8E\uDD90-\uDD9C\uDDA0\uDDD0-\uDDFC]|\uD802[\uDC77\uDC78\uDEC8]|\uD805\uDF3F|\uD807[\uDFD5-\uDFF1]|\uD81A[\uDF3C-\uDF3F\uDF45]|\uD82F\uDC9C|\uD833[\uDF50-\uDFC3]|\uD834[\uDC00-\uDCF5\uDD00-\uDD26\uDD29-\uDD64\uDD6A-\uDD6C\uDD83\uDD84\uDD8C-\uDDA9\uDDAE-\uDDEA\uDE00-\uDE41\uDE45\uDF00-\uDF56]|\uD835[\uDEC1\uDEDB\uDEFB\uDF15\uDF35\uDF4F\uDF6F\uDF89\uDFA9\uDFC3]|\uD836[\uDC00-\uDDFF\uDE37-\uDE3A\uDE6D-\uDE74\uDE76-\uDE83\uDE85\uDE86]|\uD838[\uDD4F\uDEFF]|\uD83B[\uDCAC\uDCB0\uDD2E\uDEF0\uDEF1]|\uD83C[\uDC00-\uDC2B\uDC30-\uDC93\uDCA0-\uDCAE\uDCB1-\uDCBF\uDCC1-\uDCCF\uDCD1-\uDCF5\uDD0D-\uDDAD\uDDE6-\uDE02\uDE10-\uDE3B\uDE40-\uDE48\uDE50\uDE51\uDE60-\uDE65\uDF00-\uDFFF]|\uD83D[\uDC00-\uDED7\uDEDC-\uDEEC\uDEF0-\uDEFC\uDF00-\uDF76\uDF7B-\uDFD9\uDFE0-\uDFEB\uDFF0]|\uD83E[\uDC00-\uDC0B\uDC10-\uDC47\uDC50-\uDC59\uDC60-\uDC87\uDC90-\uDCAD\uDCB0\uDCB1\uDD00-\uDE53\uDE60-\uDE6D\uDE70-\uDE7C\uDE80-\uDE88\uDE90-\uDEBD\uDEBF-\uDEC5\uDECE-\uDEDB\uDEE0-\uDEE8\uDEF0-\uDEF8\uDF00-\uDF92\uDF94-\uDFCA]/,te=/[ \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000]/,re=Object.freeze({__proto__:null,Any:X,Cc:Y,Cf:/[\xAD\u0600-\u0605\u061C\u06DD\u070F\u0890\u0891\u08E2\u180E\u200B-\u200F\u202A-\u202E\u2060-\u2064\u2066-\u206F\uFEFF\uFFF9-\uFFFB]|\uD804[\uDCBD\uDCCD]|\uD80D[\uDC30-\uDC3F]|\uD82F[\uDCA0-\uDCA3]|\uD834[\uDD73-\uDD7A]|\uDB40[\uDC01\uDC20-\uDC7F]/,P:K,S:ee,Z:te}),ne=new Uint16Array('ᵁ<Õıʊҝջאٵ۞ޢߖࠏ੊ઑඡ๭༉༦჊ረዡᐕᒝᓃᓟᔥ\0\0\0\0\0\0ᕫᛍᦍᰒᷝ὾⁠↰⊍⏀⏻⑂⠤⤒ⴈ⹈⿎〖㊺㘹㞬㣾㨨㩱㫠㬮ࠀEMabcfglmnoprstu\\bfms¦³¹ÈÏlig耻Æ䃆P耻&䀦cute耻Á䃁reve;䄂Āiyx}rc耻Â䃂;䐐r;쀀𝔄rave耻À䃀pha;䎑acr;䄀d;橓Āgp¡on;䄄f;쀀𝔸plyFunction;恡ing耻Å䃅Ācs¾Ãr;쀀𝒜ign;扔ilde耻Ã䃃ml耻Ä䃄ЀaceforsuåûþėĜĢħĪĀcrêòkslash;或Ŷöø;櫧ed;挆y;䐑ƀcrtąċĔause;戵noullis;愬a;䎒r;쀀𝔅pf;쀀𝔹eve;䋘còēmpeq;扎܀HOacdefhilorsuōőŖƀƞƢƵƷƺǜȕɳɸɾcy;䐧PY耻©䂩ƀcpyŝŢźute;䄆Ā;iŧŨ拒talDifferentialD;慅leys;愭ȀaeioƉƎƔƘron;䄌dil耻Ç䃇rc;䄈nint;戰ot;䄊ĀdnƧƭilla;䂸terDot;䂷òſi;䎧rcleȀDMPTǇǋǑǖot;抙inus;抖lus;投imes;抗oĀcsǢǸkwiseContourIntegral;戲eCurlyĀDQȃȏoubleQuote;思uote;怙ȀlnpuȞȨɇɕonĀ;eȥȦ户;橴ƀgitȯȶȺruent;扡nt;戯ourIntegral;戮ĀfrɌɎ;愂oduct;成nterClockwiseContourIntegral;戳oss;樯cr;쀀𝒞pĀ;Cʄʅ拓ap;才րDJSZacefiosʠʬʰʴʸˋ˗ˡ˦̳ҍĀ;oŹʥtrahd;椑cy;䐂cy;䐅cy;䐏ƀgrsʿ˄ˇger;怡r;憡hv;櫤Āayː˕ron;䄎;䐔lĀ;t˝˞戇a;䎔r;쀀𝔇Āaf˫̧Ācm˰̢riticalȀADGT̖̜̀̆cute;䂴oŴ̋̍;䋙bleAcute;䋝rave;䁠ilde;䋜ond;拄ferentialD;慆Ѱ̽\0\0\0͔͂\0Ѕf;쀀𝔻ƀ;DE͈͉͍䂨ot;惜qual;扐blèCDLRUVͣͲ΂ϏϢϸontourIntegraìȹoɴ͹\0\0ͻ»͉nArrow;懓Āeo·ΤftƀARTΐΖΡrrow;懐ightArrow;懔eåˊngĀLRΫτeftĀARγιrrow;柸ightArrow;柺ightArrow;柹ightĀATϘϞrrow;懒ee;抨pɁϩ\0\0ϯrrow;懑ownArrow;懕erticalBar;戥ǹABLRTaВЪаўѿͼrrowƀ;BUНОТ憓ar;椓pArrow;懵reve;䌑eft˒к\0ц\0ѐightVector;楐eeVector;楞ectorĀ;Bљњ憽ar;楖ightǔѧ\0ѱeeVector;楟ectorĀ;BѺѻ懁ar;楗eeĀ;A҆҇护rrow;憧ĀctҒҗr;쀀𝒟rok;䄐ࠀNTacdfglmopqstuxҽӀӄӋӞӢӧӮӵԡԯԶՒ՝ՠեG;䅊H耻Ð䃐cute耻É䃉ƀaiyӒӗӜron;䄚rc耻Ê䃊;䐭ot;䄖r;쀀𝔈rave耻È䃈ement;戈ĀapӺӾcr;䄒tyɓԆ\0\0ԒmallSquare;旻erySmallSquare;斫ĀgpԦԪon;䄘f;쀀𝔼silon;䎕uĀaiԼՉlĀ;TՂՃ橵ilde;扂librium;懌Āci՗՚r;愰m;橳a;䎗ml耻Ë䃋Āipժկsts;戃onentialE;慇ʀcfiosօֈ֍ֲ׌y;䐤r;쀀𝔉lledɓ֗\0\0֣mallSquare;旼erySmallSquare;斪Ͱֺ\0ֿ\0\0ׄf;쀀𝔽All;戀riertrf;愱cò׋؀JTabcdfgorstר׬ׯ׺؀ؒؖ؛؝أ٬ٲcy;䐃耻>䀾mmaĀ;d׷׸䎓;䏜reve;䄞ƀeiy؇،ؐdil;䄢rc;䄜;䐓ot;䄠r;쀀𝔊;拙pf;쀀𝔾eater̀EFGLSTصلَٖٛ٦qualĀ;Lؾؿ扥ess;招ullEqual;执reater;檢ess;扷lantEqual;橾ilde;扳cr;쀀𝒢;扫ЀAacfiosuڅڋږڛڞڪھۊRDcy;䐪Āctڐڔek;䋇;䁞irc;䄤r;愌lbertSpace;愋ǰگ\0ڲf;愍izontalLine;攀Āctۃۅòکrok;䄦mpńېۘownHumðįqual;扏܀EJOacdfgmnostuۺ۾܃܇܎ܚܞܡܨ݄ݸދޏޕcy;䐕lig;䄲cy;䐁cute耻Í䃍Āiyܓܘrc耻Î䃎;䐘ot;䄰r;愑rave耻Ì䃌ƀ;apܠܯܿĀcgܴܷr;䄪inaryI;慈lieóϝǴ݉\0ݢĀ;eݍݎ戬Āgrݓݘral;戫section;拂isibleĀCTݬݲomma;恣imes;恢ƀgptݿރވon;䄮f;쀀𝕀a;䎙cr;愐ilde;䄨ǫޚ\0ޞcy;䐆l耻Ï䃏ʀcfosuެ޷޼߂ߐĀiyޱ޵rc;䄴;䐙r;쀀𝔍pf;쀀𝕁ǣ߇\0ߌr;쀀𝒥rcy;䐈kcy;䐄΀HJacfosߤߨ߽߬߱ࠂࠈcy;䐥cy;䐌ppa;䎚Āey߶߻dil;䄶;䐚r;쀀𝔎pf;쀀𝕂cr;쀀𝒦րJTaceflmostࠥࠩࠬࡐࡣ঳সে্਷ੇcy;䐉耻<䀼ʀcmnpr࠷࠼ࡁࡄࡍute;䄹bda;䎛g;柪lacetrf;愒r;憞ƀaeyࡗ࡜ࡡron;䄽dil;䄻;䐛Āfsࡨ॰tԀACDFRTUVarࡾࢩࢱࣦ࣠ࣼयज़ΐ४Ānrࢃ࢏gleBracket;柨rowƀ;BR࢙࢚࢞憐ar;懤ightArrow;懆eiling;挈oǵࢷ\0ࣃbleBracket;柦nǔࣈ\0࣒eeVector;楡ectorĀ;Bࣛࣜ懃ar;楙loor;挊ightĀAV࣯ࣵrrow;憔ector;楎Āerँगeƀ;AVउऊऐ抣rrow;憤ector;楚iangleƀ;BEतथऩ抲ar;槏qual;抴pƀDTVषूौownVector;楑eeVector;楠ectorĀ;Bॖॗ憿ar;楘ectorĀ;B॥०憼ar;楒ightáΜs̀EFGLSTॾঋকঝঢভqualGreater;拚ullEqual;扦reater;扶ess;檡lantEqual;橽ilde;扲r;쀀𝔏Ā;eঽা拘ftarrow;懚idot;䄿ƀnpw৔ਖਛgȀLRlr৞৷ਂਐeftĀAR০৬rrow;柵ightArrow;柷ightArrow;柶eftĀarγਊightáοightáϊf;쀀𝕃erĀLRਢਬeftArrow;憙ightArrow;憘ƀchtਾੀੂòࡌ;憰rok;䅁;扪Ѐacefiosuਗ਼੝੠੷੼અઋ઎p;椅y;䐜Ādl੥੯iumSpace;恟lintrf;愳r;쀀𝔐nusPlus;戓pf;쀀𝕄cò੶;䎜ҀJacefostuણધભીଔଙඑ඗ඞcy;䐊cute;䅃ƀaey઴હાron;䅇dil;䅅;䐝ƀgswે૰଎ativeƀMTV૓૟૨ediumSpace;怋hiĀcn૦૘ë૙eryThiî૙tedĀGL૸ଆreaterGreateòٳessLesóੈLine;䀊r;쀀𝔑ȀBnptଢନଷ଺reak;恠BreakingSpace;䂠f;愕ڀ;CDEGHLNPRSTV୕ୖ୪୼஡௫ఄ౞಄ದ೘ൡඅ櫬Āou୛୤ngruent;扢pCap;扭oubleVerticalBar;戦ƀlqxஃஊ஛ement;戉ualĀ;Tஒஓ扠ilde;쀀≂̸ists;戄reater΀;EFGLSTஶஷ஽௉௓௘௥扯qual;扱ullEqual;쀀≧̸reater;쀀≫̸ess;批lantEqual;쀀⩾̸ilde;扵umpń௲௽ownHump;쀀≎̸qual;쀀≏̸eĀfsఊధtTriangleƀ;BEచఛడ拪ar;쀀⧏̸qual;括s̀;EGLSTవశ఼ౄోౘ扮qual;扰reater;扸ess;쀀≪̸lantEqual;쀀⩽̸ilde;扴estedĀGL౨౹reaterGreater;쀀⪢̸essLess;쀀⪡̸recedesƀ;ESಒಓಛ技qual;쀀⪯̸lantEqual;拠ĀeiಫಹverseElement;戌ghtTriangleƀ;BEೋೌ೒拫ar;쀀⧐̸qual;拭ĀquೝഌuareSuĀbp೨೹setĀ;E೰ೳ쀀⊏̸qual;拢ersetĀ;Eഃആ쀀⊐̸qual;拣ƀbcpഓതൎsetĀ;Eഛഞ쀀⊂⃒qual;抈ceedsȀ;ESTലള഻െ抁qual;쀀⪰̸lantEqual;拡ilde;쀀≿̸ersetĀ;E൘൛쀀⊃⃒qual;抉ildeȀ;EFT൮൯൵ൿ扁qual;扄ullEqual;扇ilde;扉erticalBar;戤cr;쀀𝒩ilde耻Ñ䃑;䎝܀Eacdfgmoprstuvලෂ෉෕ෛ෠෧෼ขภยา฿ไlig;䅒cute耻Ó䃓Āiy෎ීrc耻Ô䃔;䐞blac;䅐r;쀀𝔒rave耻Ò䃒ƀaei෮ෲ෶cr;䅌ga;䎩cron;䎟pf;쀀𝕆enCurlyĀDQฎบoubleQuote;怜uote;怘;橔Āclวฬr;쀀𝒪ash耻Ø䃘iŬื฼de耻Õ䃕es;樷ml耻Ö䃖erĀBP๋๠Āar๐๓r;怾acĀek๚๜;揞et;掴arenthesis;揜Ҁacfhilors๿ງຊຏຒດຝະ໼rtialD;戂y;䐟r;쀀𝔓i;䎦;䎠usMinus;䂱Āipຢອncareplanåڝf;愙Ȁ;eio຺ູ໠໤檻cedesȀ;EST່້໏໚扺qual;檯lantEqual;扼ilde;找me;怳Ādp໩໮uct;戏ortionĀ;aȥ໹l;戝Āci༁༆r;쀀𝒫;䎨ȀUfos༑༖༛༟OT耻"䀢r;쀀𝔔pf;愚cr;쀀𝒬؀BEacefhiorsu༾གྷཇའཱིྦྷྪྭ႖ႩႴႾarr;椐G耻®䂮ƀcnrཎནབute;䅔g;柫rĀ;tཛྷཝ憠l;椖ƀaeyཧཬཱron;䅘dil;䅖;䐠Ā;vླྀཹ愜erseĀEUྂྙĀlq྇ྎement;戋uilibrium;懋pEquilibrium;楯r»ཹo;䎡ghtЀACDFTUVa࿁࿫࿳ဢဨၛႇϘĀnr࿆࿒gleBracket;柩rowƀ;BL࿜࿝࿡憒ar;懥eftArrow;懄eiling;按oǵ࿹\0စbleBracket;柧nǔည\0နeeVector;楝ectorĀ;Bဝသ懂ar;楕loor;挋Āerိ၃eƀ;AVဵံြ抢rrow;憦ector;楛iangleƀ;BEၐၑၕ抳ar;槐qual;抵pƀDTVၣၮၸownVector;楏eeVector;楜ectorĀ;Bႂႃ憾ar;楔ectorĀ;B႑႒懀ar;楓Āpuႛ႞f;愝ndImplies;楰ightarrow;懛ĀchႹႼr;愛;憱leDelayed;槴ڀHOacfhimoqstuფჱჷჽᄙᄞᅑᅖᅡᅧᆵᆻᆿĀCcჩხHcy;䐩y;䐨FTcy;䐬cute;䅚ʀ;aeiyᄈᄉᄎᄓᄗ檼ron;䅠dil;䅞rc;䅜;䐡r;쀀𝔖ortȀDLRUᄪᄴᄾᅉownArrow»ОeftArrow»࢚ightArrow»࿝pArrow;憑gma;䎣allCircle;战pf;쀀𝕊ɲᅭ\0\0ᅰt;戚areȀ;ISUᅻᅼᆉᆯ斡ntersection;抓uĀbpᆏᆞsetĀ;Eᆗᆘ抏qual;抑ersetĀ;Eᆨᆩ抐qual;抒nion;抔cr;쀀𝒮ar;拆ȀbcmpᇈᇛሉላĀ;sᇍᇎ拐etĀ;Eᇍᇕqual;抆ĀchᇠህeedsȀ;ESTᇭᇮᇴᇿ扻qual;檰lantEqual;扽ilde;承Tháྌ;我ƀ;esሒሓሣ拑rsetĀ;Eሜም抃qual;抇et»ሓրHRSacfhiorsሾቄ቉ቕ቞ቱቶኟዂወዑORN耻Þ䃞ADE;愢ĀHc቎ቒcy;䐋y;䐦Ābuቚቜ;䀉;䎤ƀaeyብቪቯron;䅤dil;䅢;䐢r;쀀𝔗Āeiቻ኉ǲኀ\0ኇefore;戴a;䎘Ācn኎ኘkSpace;쀀  Space;怉ldeȀ;EFTካኬኲኼ戼qual;扃ullEqual;扅ilde;扈pf;쀀𝕋ipleDot;惛Āctዖዛr;쀀𝒯rok;䅦ૡዷጎጚጦ\0ጬጱ\0\0\0\0\0ጸጽ፷ᎅ\0᏿ᐄᐊᐐĀcrዻጁute耻Ú䃚rĀ;oጇገ憟cir;楉rǣጓ\0጖y;䐎ve;䅬Āiyጞጣrc耻Û䃛;䐣blac;䅰r;쀀𝔘rave耻Ù䃙acr;䅪Ādiፁ፩erĀBPፈ፝Āarፍፐr;䁟acĀekፗፙ;揟et;掵arenthesis;揝onĀ;P፰፱拃lus;抎Āgp፻፿on;䅲f;쀀𝕌ЀADETadps᎕ᎮᎸᏄϨᏒᏗᏳrrowƀ;BDᅐᎠᎤar;椒ownArrow;懅ownArrow;憕quilibrium;楮eeĀ;AᏋᏌ报rrow;憥ownáϳerĀLRᏞᏨeftArrow;憖ightArrow;憗iĀ;lᏹᏺ䏒on;䎥ing;䅮cr;쀀𝒰ilde;䅨ml耻Ü䃜ҀDbcdefosvᐧᐬᐰᐳᐾᒅᒊᒐᒖash;披ar;櫫y;䐒ashĀ;lᐻᐼ抩;櫦Āerᑃᑅ;拁ƀbtyᑌᑐᑺar;怖Ā;iᑏᑕcalȀBLSTᑡᑥᑪᑴar;戣ine;䁼eparator;杘ilde;所ThinSpace;怊r;쀀𝔙pf;쀀𝕍cr;쀀𝒱dash;抪ʀcefosᒧᒬᒱᒶᒼirc;䅴dge;拀r;쀀𝔚pf;쀀𝕎cr;쀀𝒲Ȁfiosᓋᓐᓒᓘr;쀀𝔛;䎞pf;쀀𝕏cr;쀀𝒳ҀAIUacfosuᓱᓵᓹᓽᔄᔏᔔᔚᔠcy;䐯cy;䐇cy;䐮cute耻Ý䃝Āiyᔉᔍrc;䅶;䐫r;쀀𝔜pf;쀀𝕐cr;쀀𝒴ml;䅸ЀHacdefosᔵᔹᔿᕋᕏᕝᕠᕤcy;䐖cute;䅹Āayᕄᕉron;䅽;䐗ot;䅻ǲᕔ\0ᕛoWidtè૙a;䎖r;愨pf;愤cr;쀀𝒵௡ᖃᖊᖐ\0ᖰᖶᖿ\0\0\0\0ᗆᗛᗫᙟ᙭\0ᚕ᚛ᚲᚹ\0ᚾcute耻á䃡reve;䄃̀;Ediuyᖜᖝᖡᖣᖨᖭ戾;쀀∾̳;房rc耻â䃢te肻´̆;䐰lig耻æ䃦Ā;r²ᖺ;쀀𝔞rave耻à䃠ĀepᗊᗖĀfpᗏᗔsym;愵èᗓha;䎱ĀapᗟcĀclᗤᗧr;䄁g;樿ɤᗰ\0\0ᘊʀ;adsvᗺᗻᗿᘁᘇ戧nd;橕;橜lope;橘;橚΀;elmrszᘘᘙᘛᘞᘿᙏᙙ戠;榤e»ᘙsdĀ;aᘥᘦ戡ѡᘰᘲᘴᘶᘸᘺᘼᘾ;榨;榩;榪;榫;榬;榭;榮;榯tĀ;vᙅᙆ戟bĀ;dᙌᙍ抾;榝Āptᙔᙗh;戢»¹arr;捼Āgpᙣᙧon;䄅f;쀀𝕒΀;Eaeiop዁ᙻᙽᚂᚄᚇᚊ;橰cir;橯;扊d;手s;䀧roxĀ;e዁ᚒñᚃing耻å䃥ƀctyᚡᚦᚨr;쀀𝒶;䀪mpĀ;e዁ᚯñʈilde耻ã䃣ml耻ä䃤Āciᛂᛈoninôɲnt;樑ࠀNabcdefiklnoprsu᛭ᛱᜰ᜼ᝃᝈ᝸᝽០៦ᠹᡐᜍ᤽᥈ᥰot;櫭Ācrᛶ᜞kȀcepsᜀᜅᜍᜓong;扌psilon;䏶rime;怵imĀ;e᜚᜛戽q;拍Ŷᜢᜦee;抽edĀ;gᜬᜭ挅e»ᜭrkĀ;t፜᜷brk;掶Āoyᜁᝁ;䐱quo;怞ʀcmprtᝓ᝛ᝡᝤᝨausĀ;eĊĉptyv;榰séᜌnoõēƀahwᝯ᝱ᝳ;䎲;愶een;扬r;쀀𝔟g΀costuvwឍឝឳេ៕៛៞ƀaiuបពរðݠrc;旯p»፱ƀdptឤឨឭot;樀lus;樁imes;樂ɱឹ\0\0ើcup;樆ar;昅riangleĀdu៍្own;施p;斳plus;樄eåᑄåᒭarow;植ƀako៭ᠦᠵĀcn៲ᠣkƀlst៺֫᠂ozenge;槫riangleȀ;dlr᠒᠓᠘᠝斴own;斾eft;旂ight;斸k;搣Ʊᠫ\0ᠳƲᠯ\0ᠱ;斒;斑4;斓ck;斈ĀeoᠾᡍĀ;qᡃᡆ쀀=⃥uiv;쀀≡⃥t;挐Ȁptwxᡙᡞᡧᡬf;쀀𝕓Ā;tᏋᡣom»Ꮜtie;拈؀DHUVbdhmptuvᢅᢖᢪᢻᣗᣛᣬ᣿ᤅᤊᤐᤡȀLRlrᢎᢐᢒᢔ;敗;敔;敖;敓ʀ;DUduᢡᢢᢤᢦᢨ敐;敦;敩;敤;敧ȀLRlrᢳᢵᢷᢹ;敝;敚;敜;教΀;HLRhlrᣊᣋᣍᣏᣑᣓᣕ救;敬;散;敠;敫;敢;敟ox;槉ȀLRlrᣤᣦᣨᣪ;敕;敒;攐;攌ʀ;DUduڽ᣷᣹᣻᣽;敥;敨;攬;攴inus;抟lus;択imes;抠ȀLRlrᤙᤛᤝ᤟;敛;敘;攘;攔΀;HLRhlrᤰᤱᤳᤵᤷ᤻᤹攂;敪;敡;敞;攼;攤;攜Āevģ᥂bar耻¦䂦Ȁceioᥑᥖᥚᥠr;쀀𝒷mi;恏mĀ;e᜚᜜lƀ;bhᥨᥩᥫ䁜;槅sub;柈Ŭᥴ᥾lĀ;e᥹᥺怢t»᥺pƀ;Eeįᦅᦇ;檮Ā;qۜۛೡᦧ\0᧨ᨑᨕᨲ\0ᨷᩐ\0\0᪴\0\0᫁\0\0ᬡᬮ᭍᭒\0᯽\0ᰌƀcpr᦭ᦲ᧝ute;䄇̀;abcdsᦿᧀᧄ᧊᧕᧙戩nd;橄rcup;橉Āau᧏᧒p;橋p;橇ot;橀;쀀∩︀Āeo᧢᧥t;恁îړȀaeiu᧰᧻ᨁᨅǰ᧵\0᧸s;橍on;䄍dil耻ç䃧rc;䄉psĀ;sᨌᨍ橌m;橐ot;䄋ƀdmnᨛᨠᨦil肻¸ƭptyv;榲t脀¢;eᨭᨮ䂢räƲr;쀀𝔠ƀceiᨽᩀᩍy;䑇ckĀ;mᩇᩈ朓ark»ᩈ;䏇r΀;Ecefms᩟᩠ᩢᩫ᪤᪪᪮旋;槃ƀ;elᩩᩪᩭ䋆q;扗eɡᩴ\0\0᪈rrowĀlr᩼᪁eft;憺ight;憻ʀRSacd᪒᪔᪖᪚᪟»ཇ;擈st;抛irc;抚ash;抝nint;樐id;櫯cir;槂ubsĀ;u᪻᪼晣it»᪼ˬ᫇᫔᫺\0ᬊonĀ;eᫍᫎ䀺Ā;qÇÆɭ᫙\0\0᫢aĀ;t᫞᫟䀬;䁀ƀ;fl᫨᫩᫫戁îᅠeĀmx᫱᫶ent»᫩eóɍǧ᫾\0ᬇĀ;dኻᬂot;橭nôɆƀfryᬐᬔᬗ;쀀𝕔oäɔ脀©;sŕᬝr;愗Āaoᬥᬩrr;憵ss;朗Ācuᬲᬷr;쀀𝒸Ābpᬼ᭄Ā;eᭁᭂ櫏;櫑Ā;eᭉᭊ櫐;櫒dot;拯΀delprvw᭠᭬᭷ᮂᮬᯔ᯹arrĀlr᭨᭪;椸;椵ɰ᭲\0\0᭵r;拞c;拟arrĀ;p᭿ᮀ憶;椽̀;bcdosᮏᮐᮖᮡᮥᮨ截rcap;橈Āauᮛᮞp;橆p;橊ot;抍r;橅;쀀∪︀Ȁalrv᮵ᮿᯞᯣrrĀ;mᮼᮽ憷;椼yƀevwᯇᯔᯘqɰᯎ\0\0ᯒreã᭳uã᭵ee;拎edge;拏en耻¤䂤earrowĀlrᯮ᯳eft»ᮀight»ᮽeäᯝĀciᰁᰇoninôǷnt;戱lcty;挭ঀAHabcdefhijlorstuwz᰸᰻᰿ᱝᱩᱵᲊᲞᲬᲷ᳻᳿ᴍᵻᶑᶫᶻ᷆᷍rò΁ar;楥Ȁglrs᱈ᱍ᱒᱔ger;怠eth;愸òᄳhĀ;vᱚᱛ怐»ऊūᱡᱧarow;椏aã̕Āayᱮᱳron;䄏;䐴ƀ;ao̲ᱼᲄĀgrʿᲁr;懊tseq;橷ƀglmᲑᲔᲘ耻°䂰ta;䎴ptyv;榱ĀirᲣᲨsht;楿;쀀𝔡arĀlrᲳᲵ»ࣜ»သʀaegsv᳂͸᳖᳜᳠mƀ;oș᳊᳔ndĀ;ș᳑uit;晦amma;䏝in;拲ƀ;io᳧᳨᳸䃷de脀÷;o᳧ᳰntimes;拇nø᳷cy;䑒cɯᴆ\0\0ᴊrn;挞op;挍ʀlptuwᴘᴝᴢᵉᵕlar;䀤f;쀀𝕕ʀ;emps̋ᴭᴷᴽᵂqĀ;d͒ᴳot;扑inus;戸lus;戔quare;抡blebarwedgåúnƀadhᄮᵝᵧownarrowóᲃarpoonĀlrᵲᵶefôᲴighôᲶŢᵿᶅkaro÷གɯᶊ\0\0ᶎrn;挟op;挌ƀcotᶘᶣᶦĀryᶝᶡ;쀀𝒹;䑕l;槶rok;䄑Ādrᶰᶴot;拱iĀ;fᶺ᠖斿Āah᷀᷃ròЩaòྦangle;榦Āci᷒ᷕy;䑟grarr;柿ऀDacdefglmnopqrstuxḁḉḙḸոḼṉṡṾấắẽỡἪἷὄ὎὚ĀDoḆᴴoôᲉĀcsḎḔute耻é䃩ter;橮ȀaioyḢḧḱḶron;䄛rĀ;cḭḮ扖耻ê䃪lon;払;䑍ot;䄗ĀDrṁṅot;扒;쀀𝔢ƀ;rsṐṑṗ檚ave耻è䃨Ā;dṜṝ檖ot;檘Ȁ;ilsṪṫṲṴ檙nters;揧;愓Ā;dṹṺ檕ot;檗ƀapsẅẉẗcr;䄓tyƀ;svẒẓẕ戅et»ẓpĀ1;ẝẤĳạả;怄;怅怃ĀgsẪẬ;䅋p;怂ĀgpẴẸon;䄙f;쀀𝕖ƀalsỄỎỒrĀ;sỊị拕l;槣us;橱iƀ;lvỚớở䎵on»ớ;䏵ȀcsuvỪỳἋἣĀioữḱrc»Ḯɩỹ\0\0ỻíՈantĀglἂἆtr»ṝess»Ṻƀaeiἒ἖Ἒls;䀽st;扟vĀ;DȵἠD;橸parsl;槥ĀDaἯἳot;打rr;楱ƀcdiἾὁỸr;愯oô͒ĀahὉὋ;䎷耻ð䃰Āmrὓὗl耻ë䃫o;悬ƀcipὡὤὧl;䀡sôծĀeoὬὴctatioîՙnentialåչৡᾒ\0ᾞ\0ᾡᾧ\0\0ῆῌ\0ΐ\0ῦῪ \0 ⁚llingdotseñṄy;䑄male;晀ƀilrᾭᾳ῁lig;耀ﬃɩᾹ\0\0᾽g;耀ﬀig;耀ﬄ;쀀𝔣lig;耀ﬁlig;쀀fjƀaltῙ῜ῡt;晭ig;耀ﬂns;斱of;䆒ǰ΅\0ῳf;쀀𝕗ĀakֿῷĀ;vῼ´拔;櫙artint;樍Āao‌⁕Ācs‑⁒α‚‰‸⁅⁈\0⁐β•‥‧‪‬\0‮耻½䂽;慓耻¼䂼;慕;慙;慛Ƴ‴\0‶;慔;慖ʴ‾⁁\0\0⁃耻¾䂾;慗;慜5;慘ƶ⁌\0⁎;慚;慝8;慞l;恄wn;挢cr;쀀𝒻ࢀEabcdefgijlnorstv₂₉₟₥₰₴⃰⃵⃺⃿℃ℒℸ̗ℾ⅒↞Ā;lٍ₇;檌ƀcmpₐₕ₝ute;䇵maĀ;dₜ᳚䎳;檆reve;䄟Āiy₪₮rc;䄝;䐳ot;䄡Ȁ;lqsؾق₽⃉ƀ;qsؾٌ⃄lanô٥Ȁ;cdl٥⃒⃥⃕c;檩otĀ;o⃜⃝檀Ā;l⃢⃣檂;檄Ā;e⃪⃭쀀⋛︀s;檔r;쀀𝔤Ā;gٳ؛mel;愷cy;䑓Ȁ;Eajٚℌℎℐ;檒;檥;檤ȀEaesℛℝ℩ℴ;扩pĀ;p℣ℤ檊rox»ℤĀ;q℮ℯ檈Ā;q℮ℛim;拧pf;쀀𝕘Āci⅃ⅆr;愊mƀ;el٫ⅎ⅐;檎;檐茀>;cdlqr׮ⅠⅪⅮⅳⅹĀciⅥⅧ;檧r;橺ot;拗Par;榕uest;橼ʀadelsↄⅪ←ٖ↛ǰ↉\0↎proø₞r;楸qĀlqؿ↖lesó₈ií٫Āen↣↭rtneqq;쀀≩︀Å↪ԀAabcefkosy⇄⇇⇱⇵⇺∘∝∯≨≽ròΠȀilmr⇐⇔⇗⇛rsðᒄf»․ilôکĀdr⇠⇤cy;䑊ƀ;cwࣴ⇫⇯ir;楈;憭ar;意irc;䄥ƀalr∁∎∓rtsĀ;u∉∊晥it»∊lip;怦con;抹r;쀀𝔥sĀew∣∩arow;椥arow;椦ʀamopr∺∾≃≞≣rr;懿tht;戻kĀlr≉≓eftarrow;憩ightarrow;憪f;쀀𝕙bar;怕ƀclt≯≴≸r;쀀𝒽asè⇴rok;䄧Ābp⊂⊇ull;恃hen»ᱛૡ⊣\0⊪\0⊸⋅⋎\0⋕⋳\0\0⋸⌢⍧⍢⍿\0⎆⎪⎴cute耻í䃭ƀ;iyݱ⊰⊵rc耻î䃮;䐸Ācx⊼⊿y;䐵cl耻¡䂡ĀfrΟ⋉;쀀𝔦rave耻ì䃬Ȁ;inoܾ⋝⋩⋮Āin⋢⋦nt;樌t;戭fin;槜ta;愩lig;䄳ƀaop⋾⌚⌝ƀcgt⌅⌈⌗r;䄫ƀelpܟ⌏⌓inåގarôܠh;䄱f;抷ed;䆵ʀ;cfotӴ⌬⌱⌽⍁are;愅inĀ;t⌸⌹戞ie;槝doô⌙ʀ;celpݗ⍌⍐⍛⍡al;抺Āgr⍕⍙eróᕣã⍍arhk;樗rod;樼Ȁcgpt⍯⍲⍶⍻y;䑑on;䄯f;쀀𝕚a;䎹uest耻¿䂿Āci⎊⎏r;쀀𝒾nʀ;EdsvӴ⎛⎝⎡ӳ;拹ot;拵Ā;v⎦⎧拴;拳Ā;iݷ⎮lde;䄩ǫ⎸\0⎼cy;䑖l耻ï䃯̀cfmosu⏌⏗⏜⏡⏧⏵Āiy⏑⏕rc;䄵;䐹r;쀀𝔧ath;䈷pf;쀀𝕛ǣ⏬\0⏱r;쀀𝒿rcy;䑘kcy;䑔Ѐacfghjos␋␖␢␧␭␱␵␻ppaĀ;v␓␔䎺;䏰Āey␛␠dil;䄷;䐺r;쀀𝔨reen;䄸cy;䑅cy;䑜pf;쀀𝕜cr;쀀𝓀஀ABEHabcdefghjlmnoprstuv⑰⒁⒆⒍⒑┎┽╚▀♎♞♥♹♽⚚⚲⛘❝❨➋⟀⠁⠒ƀart⑷⑺⑼rò৆òΕail;椛arr;椎Ā;gঔ⒋;檋ar;楢ॣ⒥\0⒪\0⒱\0\0\0\0\0⒵Ⓔ\0ⓆⓈⓍ\0⓹ute;䄺mptyv;榴raîࡌbda;䎻gƀ;dlࢎⓁⓃ;榑åࢎ;檅uo耻«䂫rЀ;bfhlpst࢙ⓞⓦⓩ⓫⓮⓱⓵Ā;f࢝ⓣs;椟s;椝ë≒p;憫l;椹im;楳l;憢ƀ;ae⓿─┄檫il;椙Ā;s┉┊檭;쀀⪭︀ƀabr┕┙┝rr;椌rk;杲Āak┢┬cĀek┨┪;䁻;䁛Āes┱┳;榋lĀdu┹┻;榏;榍Ȁaeuy╆╋╖╘ron;䄾Ādi═╔il;䄼ìࢰâ┩;䐻Ȁcqrs╣╦╭╽a;椶uoĀ;rนᝆĀdu╲╷har;楧shar;楋h;憲ʀ;fgqs▋▌উ◳◿扤tʀahlrt▘▤▷◂◨rrowĀ;t࢙□aé⓶arpoonĀdu▯▴own»њp»०eftarrows;懇ightƀahs◍◖◞rrowĀ;sࣴࢧarpoonó྘quigarro÷⇰hreetimes;拋ƀ;qs▋ও◺lanôবʀ;cdgsব☊☍☝☨c;檨otĀ;o☔☕橿Ā;r☚☛檁;檃Ā;e☢☥쀀⋚︀s;檓ʀadegs☳☹☽♉♋pproøⓆot;拖qĀgq♃♅ôউgtò⒌ôছiíলƀilr♕࣡♚sht;楼;쀀𝔩Ā;Eজ♣;檑š♩♶rĀdu▲♮Ā;l॥♳;楪lk;斄cy;䑙ʀ;achtੈ⚈⚋⚑⚖rò◁orneòᴈard;楫ri;旺Āio⚟⚤dot;䅀ustĀ;a⚬⚭掰che»⚭ȀEaes⚻⚽⛉⛔;扨pĀ;p⛃⛄檉rox»⛄Ā;q⛎⛏檇Ā;q⛎⚻im;拦Ѐabnoptwz⛩⛴⛷✚✯❁❇❐Ānr⛮⛱g;柬r;懽rëࣁgƀlmr⛿✍✔eftĀar০✇ightá৲apsto;柼ightá৽parrowĀlr✥✩efô⓭ight;憬ƀafl✶✹✽r;榅;쀀𝕝us;樭imes;樴š❋❏st;戗áፎƀ;ef❗❘᠀旊nge»❘arĀ;l❤❥䀨t;榓ʀachmt❳❶❼➅➇ròࢨorneòᶌarĀ;d྘➃;業;怎ri;抿̀achiqt➘➝ੀ➢➮➻quo;怹r;쀀𝓁mƀ;egল➪➬;檍;檏Ābu┪➳oĀ;rฟ➹;怚rok;䅂萀<;cdhilqrࠫ⟒☹⟜⟠⟥⟪⟰Āci⟗⟙;檦r;橹reå◲mes;拉arr;楶uest;橻ĀPi⟵⟹ar;榖ƀ;ef⠀भ᠛旃rĀdu⠇⠍shar;楊har;楦Āen⠗⠡rtneqq;쀀≨︀Å⠞܀Dacdefhilnopsu⡀⡅⢂⢎⢓⢠⢥⢨⣚⣢⣤ઃ⣳⤂Dot;戺Ȁclpr⡎⡒⡣⡽r耻¯䂯Āet⡗⡙;時Ā;e⡞⡟朠se»⡟Ā;sျ⡨toȀ;dluျ⡳⡷⡻owîҌefôएðᏑker;斮Āoy⢇⢌mma;権;䐼ash;怔asuredangle»ᘦr;쀀𝔪o;愧ƀcdn⢯⢴⣉ro耻µ䂵Ȁ;acdᑤ⢽⣀⣄sôᚧir;櫰ot肻·Ƶusƀ;bd⣒ᤃ⣓戒Ā;uᴼ⣘;横ţ⣞⣡p;櫛ò−ðઁĀdp⣩⣮els;抧f;쀀𝕞Āct⣸⣽r;쀀𝓂pos»ᖝƀ;lm⤉⤊⤍䎼timap;抸ఀGLRVabcdefghijlmoprstuvw⥂⥓⥾⦉⦘⧚⧩⨕⨚⩘⩝⪃⪕⪤⪨⬄⬇⭄⭿⮮ⰴⱧⱼ⳩Āgt⥇⥋;쀀⋙̸Ā;v⥐௏쀀≫⃒ƀelt⥚⥲⥶ftĀar⥡⥧rrow;懍ightarrow;懎;쀀⋘̸Ā;v⥻ే쀀≪⃒ightarrow;懏ĀDd⦎⦓ash;抯ash;抮ʀbcnpt⦣⦧⦬⦱⧌la»˞ute;䅄g;쀀∠⃒ʀ;Eiop඄⦼⧀⧅⧈;쀀⩰̸d;쀀≋̸s;䅉roø඄urĀ;a⧓⧔普lĀ;s⧓ସǳ⧟\0⧣p肻 ଷmpĀ;e௹ఀʀaeouy⧴⧾⨃⨐⨓ǰ⧹\0⧻;橃on;䅈dil;䅆ngĀ;dൾ⨊ot;쀀⩭̸p;橂;䐽ash;怓΀;Aadqsxஒ⨩⨭⨻⩁⩅⩐rr;懗rĀhr⨳⨶k;椤Ā;oᏲᏰot;쀀≐̸uiöୣĀei⩊⩎ar;椨í஘istĀ;s஠டr;쀀𝔫ȀEest௅⩦⩹⩼ƀ;qs஼⩭௡ƀ;qs஼௅⩴lanô௢ií௪Ā;rஶ⪁»ஷƀAap⪊⪍⪑rò⥱rr;憮ar;櫲ƀ;svྍ⪜ྌĀ;d⪡⪢拼;拺cy;䑚΀AEadest⪷⪺⪾⫂⫅⫶⫹rò⥦;쀀≦̸rr;憚r;急Ȁ;fqs఻⫎⫣⫯tĀar⫔⫙rro÷⫁ightarro÷⪐ƀ;qs఻⪺⫪lanôౕĀ;sౕ⫴»శiíౝĀ;rవ⫾iĀ;eచథiäඐĀpt⬌⬑f;쀀𝕟膀¬;in⬙⬚⬶䂬nȀ;Edvஉ⬤⬨⬮;쀀⋹̸ot;쀀⋵̸ǡஉ⬳⬵;拷;拶iĀ;vಸ⬼ǡಸ⭁⭃;拾;拽ƀaor⭋⭣⭩rȀ;ast୻⭕⭚⭟lleì୻l;쀀⫽⃥;쀀∂̸lint;樔ƀ;ceಒ⭰⭳uåಥĀ;cಘ⭸Ā;eಒ⭽ñಘȀAait⮈⮋⮝⮧rò⦈rrƀ;cw⮔⮕⮙憛;쀀⤳̸;쀀↝̸ghtarrow»⮕riĀ;eೋೖ΀chimpqu⮽⯍⯙⬄୸⯤⯯Ȁ;cerല⯆ഷ⯉uå൅;쀀𝓃ortɭ⬅\0\0⯖ará⭖mĀ;e൮⯟Ā;q൴൳suĀbp⯫⯭å೸åഋƀbcp⯶ⰑⰙȀ;Ees⯿ⰀഢⰄ抄;쀀⫅̸etĀ;eഛⰋqĀ;qണⰀcĀ;eലⰗñസȀ;EesⰢⰣൟⰧ抅;쀀⫆̸etĀ;e൘ⰮqĀ;qൠⰣȀgilrⰽⰿⱅⱇìௗlde耻ñ䃱çృiangleĀlrⱒⱜeftĀ;eచⱚñదightĀ;eೋⱥñ೗Ā;mⱬⱭ䎽ƀ;esⱴⱵⱹ䀣ro;愖p;怇ҀDHadgilrsⲏⲔⲙⲞⲣⲰⲶⳓⳣash;抭arr;椄p;쀀≍⃒ash;抬ĀetⲨⲬ;쀀≥⃒;쀀>⃒nfin;槞ƀAetⲽⳁⳅrr;椂;쀀≤⃒Ā;rⳊⳍ쀀<⃒ie;쀀⊴⃒ĀAtⳘⳜrr;椃rie;쀀⊵⃒im;쀀∼⃒ƀAan⳰⳴ⴂrr;懖rĀhr⳺⳽k;椣Ā;oᏧᏥear;椧ቓ᪕\0\0\0\0\0\0\0\0\0\0\0\0\0ⴭ\0ⴸⵈⵠⵥ⵲ⶄᬇ\0\0ⶍⶫ\0ⷈⷎ\0ⷜ⸙⸫⸾⹃Ācsⴱ᪗ute耻ó䃳ĀiyⴼⵅrĀ;c᪞ⵂ耻ô䃴;䐾ʀabios᪠ⵒⵗǈⵚlac;䅑v;樸old;榼lig;䅓Ācr⵩⵭ir;榿;쀀𝔬ͯ⵹\0\0⵼\0ⶂn;䋛ave耻ò䃲;槁Ābmⶈ෴ar;榵Ȁacitⶕ⶘ⶥⶨrò᪀Āir⶝ⶠr;榾oss;榻nå๒;槀ƀaeiⶱⶵⶹcr;䅍ga;䏉ƀcdnⷀⷅǍron;䎿;榶pf;쀀𝕠ƀaelⷔ⷗ǒr;榷rp;榹΀;adiosvⷪⷫⷮ⸈⸍⸐⸖戨rò᪆Ȁ;efmⷷⷸ⸂⸅橝rĀ;oⷾⷿ愴f»ⷿ耻ª䂪耻º䂺gof;抶r;橖lope;橗;橛ƀclo⸟⸡⸧ò⸁ash耻ø䃸l;折iŬⸯ⸴de耻õ䃵esĀ;aǛ⸺s;樶ml耻ö䃶bar;挽ૡ⹞\0⹽\0⺀⺝\0⺢⺹\0\0⻋ຜ\0⼓\0\0⼫⾼\0⿈rȀ;astЃ⹧⹲຅脀¶;l⹭⹮䂶leìЃɩ⹸\0\0⹻m;櫳;櫽y;䐿rʀcimpt⺋⺏⺓ᡥ⺗nt;䀥od;䀮il;怰enk;怱r;쀀𝔭ƀimo⺨⺰⺴Ā;v⺭⺮䏆;䏕maô੶ne;明ƀ;tv⺿⻀⻈䏀chfork»´;䏖Āau⻏⻟nĀck⻕⻝kĀ;h⇴⻛;愎ö⇴sҀ;abcdemst⻳⻴ᤈ⻹⻽⼄⼆⼊⼎䀫cir;樣ir;樢Āouᵀ⼂;樥;橲n肻±ຝim;樦wo;樧ƀipu⼙⼠⼥ntint;樕f;쀀𝕡nd耻£䂣Ԁ;Eaceinosu່⼿⽁⽄⽇⾁⾉⾒⽾⾶;檳p;檷uå໙Ā;c໎⽌̀;acens່⽙⽟⽦⽨⽾pproø⽃urlyeñ໙ñ໎ƀaes⽯⽶⽺pprox;檹qq;檵im;拨iíໟmeĀ;s⾈ຮ怲ƀEas⽸⾐⽺ð⽵ƀdfp໬⾙⾯ƀals⾠⾥⾪lar;挮ine;挒urf;挓Ā;t໻⾴ï໻rel;抰Āci⿀⿅r;쀀𝓅;䏈ncsp;怈̀fiopsu⿚⋢⿟⿥⿫⿱r;쀀𝔮pf;쀀𝕢rime;恗cr;쀀𝓆ƀaeo⿸〉〓tĀei⿾々rnionóڰnt;樖stĀ;e【】䀿ñἙô༔઀ABHabcdefhilmnoprstux぀けさすムㄎㄫㅇㅢㅲㆎ㈆㈕㈤㈩㉘㉮㉲㊐㊰㊷ƀartぇおがròႳòϝail;検aròᱥar;楤΀cdenqrtとふへみわゔヌĀeuねぱ;쀀∽̱te;䅕iãᅮmptyv;榳gȀ;del࿑らるろ;榒;榥å࿑uo耻»䂻rր;abcfhlpstw࿜ガクシスゼゾダッデナp;極Ā;f࿠ゴs;椠;椳s;椞ë≝ð✮l;楅im;楴l;憣;憝Āaiパフil;椚oĀ;nホボ戶aló༞ƀabrョリヮrò៥rk;杳ĀakンヽcĀekヹ・;䁽;䁝Āes㄂㄄;榌lĀduㄊㄌ;榎;榐Ȁaeuyㄗㄜㄧㄩron;䅙Ādiㄡㄥil;䅗ì࿲âヺ;䑀Ȁclqsㄴㄷㄽㅄa;椷dhar;楩uoĀ;rȎȍh;憳ƀacgㅎㅟངlȀ;ipsླྀㅘㅛႜnåႻarôྩt;断ƀilrㅩဣㅮsht;楽;쀀𝔯ĀaoㅷㆆrĀduㅽㅿ»ѻĀ;l႑ㆄ;楬Ā;vㆋㆌ䏁;䏱ƀgns㆕ㇹㇼht̀ahlrstㆤㆰ㇂㇘㇤㇮rrowĀ;t࿜ㆭaéトarpoonĀduㆻㆿowîㅾp»႒eftĀah㇊㇐rrowó࿪arpoonóՑightarrows;應quigarro÷ニhreetimes;拌g;䋚ingdotseñἲƀahm㈍㈐㈓rò࿪aòՑ;怏oustĀ;a㈞㈟掱che»㈟mid;櫮Ȁabpt㈲㈽㉀㉒Ānr㈷㈺g;柭r;懾rëဃƀafl㉇㉊㉎r;榆;쀀𝕣us;樮imes;樵Āap㉝㉧rĀ;g㉣㉤䀩t;榔olint;樒arò㇣Ȁachq㉻㊀Ⴜ㊅quo;怺r;쀀𝓇Ābu・㊊oĀ;rȔȓƀhir㊗㊛㊠reåㇸmes;拊iȀ;efl㊪ၙᠡ㊫方tri;槎luhar;楨;愞ൡ㋕㋛㋟㌬㌸㍱\0㍺㎤\0\0㏬㏰\0㐨㑈㑚㒭㒱㓊㓱\0㘖\0\0㘳cute;䅛quï➺Ԁ;Eaceinpsyᇭ㋳㋵㋿㌂㌋㌏㌟㌦㌩;檴ǰ㋺\0㋼;檸on;䅡uåᇾĀ;dᇳ㌇il;䅟rc;䅝ƀEas㌖㌘㌛;檶p;檺im;择olint;樓iíሄ;䑁otƀ;be㌴ᵇ㌵担;橦΀Aacmstx㍆㍊㍗㍛㍞㍣㍭rr;懘rĀhr㍐㍒ë∨Ā;oਸ਼਴t耻§䂧i;䀻war;椩mĀin㍩ðnuóñt;朶rĀ;o㍶⁕쀀𝔰Ȁacoy㎂㎆㎑㎠rp;景Āhy㎋㎏cy;䑉;䑈rtɭ㎙\0\0㎜iäᑤaraì⹯耻­䂭Āgm㎨㎴maƀ;fv㎱㎲㎲䏃;䏂Ѐ;deglnprካ㏅㏉㏎㏖㏞㏡㏦ot;橪Ā;q኱ኰĀ;E㏓㏔檞;檠Ā;E㏛㏜檝;檟e;扆lus;樤arr;楲aròᄽȀaeit㏸㐈㐏㐗Āls㏽㐄lsetmé㍪hp;樳parsl;槤Ādlᑣ㐔e;挣Ā;e㐜㐝檪Ā;s㐢㐣檬;쀀⪬︀ƀflp㐮㐳㑂tcy;䑌Ā;b㐸㐹䀯Ā;a㐾㐿槄r;挿f;쀀𝕤aĀdr㑍ЂesĀ;u㑔㑕晠it»㑕ƀcsu㑠㑹㒟Āau㑥㑯pĀ;sᆈ㑫;쀀⊓︀pĀ;sᆴ㑵;쀀⊔︀uĀbp㑿㒏ƀ;esᆗᆜ㒆etĀ;eᆗ㒍ñᆝƀ;esᆨᆭ㒖etĀ;eᆨ㒝ñᆮƀ;afᅻ㒦ְrť㒫ֱ»ᅼaròᅈȀcemt㒹㒾㓂㓅r;쀀𝓈tmîñiì㐕aræᆾĀar㓎㓕rĀ;f㓔ឿ昆Āan㓚㓭ightĀep㓣㓪psiloîỠhé⺯s»⡒ʀbcmnp㓻㕞ሉ㖋㖎Ҁ;Edemnprs㔎㔏㔑㔕㔞㔣㔬㔱㔶抂;櫅ot;檽Ā;dᇚ㔚ot;櫃ult;櫁ĀEe㔨㔪;櫋;把lus;檿arr;楹ƀeiu㔽㕒㕕tƀ;en㔎㕅㕋qĀ;qᇚ㔏eqĀ;q㔫㔨m;櫇Ābp㕚㕜;櫕;櫓c̀;acensᇭ㕬㕲㕹㕻㌦pproø㋺urlyeñᇾñᇳƀaes㖂㖈㌛pproø㌚qñ㌗g;晪ڀ123;Edehlmnps㖩㖬㖯ሜ㖲㖴㗀㗉㗕㗚㗟㗨㗭耻¹䂹耻²䂲耻³䂳;櫆Āos㖹㖼t;檾ub;櫘Ā;dሢ㗅ot;櫄sĀou㗏㗒l;柉b;櫗arr;楻ult;櫂ĀEe㗤㗦;櫌;抋lus;櫀ƀeiu㗴㘉㘌tƀ;enሜ㗼㘂qĀ;qሢ㖲eqĀ;q㗧㗤m;櫈Ābp㘑㘓;櫔;櫖ƀAan㘜㘠㘭rr;懙rĀhr㘦㘨ë∮Ā;oਫ਩war;椪lig耻ß䃟௡㙑㙝㙠ዎ㙳㙹\0㙾㛂\0\0\0\0\0㛛㜃\0㜉㝬\0\0\0㞇ɲ㙖\0\0㙛get;挖;䏄rë๟ƀaey㙦㙫㙰ron;䅥dil;䅣;䑂lrec;挕r;쀀𝔱Ȁeiko㚆㚝㚵㚼ǲ㚋\0㚑eĀ4fኄኁaƀ;sv㚘㚙㚛䎸ym;䏑Ācn㚢㚲kĀas㚨㚮pproø዁im»ኬsðኞĀas㚺㚮ð዁rn耻þ䃾Ǭ̟㛆⋧es膀×;bd㛏㛐㛘䃗Ā;aᤏ㛕r;樱;樰ƀeps㛡㛣㜀á⩍Ȁ;bcf҆㛬㛰㛴ot;挶ir;櫱Ā;o㛹㛼쀀𝕥rk;櫚á㍢rime;怴ƀaip㜏㜒㝤dåቈ΀adempst㜡㝍㝀㝑㝗㝜㝟ngleʀ;dlqr㜰㜱㜶㝀㝂斵own»ᶻeftĀ;e⠀㜾ñम;扜ightĀ;e㊪㝋ñၚot;旬inus;樺lus;樹b;槍ime;樻ezium;揢ƀcht㝲㝽㞁Āry㝷㝻;쀀𝓉;䑆cy;䑛rok;䅧Āio㞋㞎xô᝷headĀlr㞗㞠eftarro÷ࡏightarrow»ཝऀAHabcdfghlmoprstuw㟐㟓㟗㟤㟰㟼㠎㠜㠣㠴㡑㡝㡫㢩㣌㣒㣪㣶ròϭar;楣Ācr㟜㟢ute耻ú䃺òᅐrǣ㟪\0㟭y;䑞ve;䅭Āiy㟵㟺rc耻û䃻;䑃ƀabh㠃㠆㠋ròᎭlac;䅱aòᏃĀir㠓㠘sht;楾;쀀𝔲rave耻ù䃹š㠧㠱rĀlr㠬㠮»ॗ»ႃlk;斀Āct㠹㡍ɯ㠿\0\0㡊rnĀ;e㡅㡆挜r»㡆op;挏ri;旸Āal㡖㡚cr;䅫肻¨͉Āgp㡢㡦on;䅳f;쀀𝕦̀adhlsuᅋ㡸㡽፲㢑㢠ownáᎳarpoonĀlr㢈㢌efô㠭ighô㠯iƀ;hl㢙㢚㢜䏅»ᏺon»㢚parrows;懈ƀcit㢰㣄㣈ɯ㢶\0\0㣁rnĀ;e㢼㢽挝r»㢽op;挎ng;䅯ri;旹cr;쀀𝓊ƀdir㣙㣝㣢ot;拰lde;䅩iĀ;f㜰㣨»᠓Āam㣯㣲rò㢨l耻ü䃼angle;榧ހABDacdeflnoprsz㤜㤟㤩㤭㦵㦸㦽㧟㧤㧨㧳㧹㧽㨁㨠ròϷarĀ;v㤦㤧櫨;櫩asèϡĀnr㤲㤷grt;榜΀eknprst㓣㥆㥋㥒㥝㥤㦖appá␕othinçẖƀhir㓫⻈㥙opô⾵Ā;hᎷ㥢ïㆍĀiu㥩㥭gmá㎳Ābp㥲㦄setneqĀ;q㥽㦀쀀⊊︀;쀀⫋︀setneqĀ;q㦏㦒쀀⊋︀;쀀⫌︀Āhr㦛㦟etá㚜iangleĀlr㦪㦯eft»थight»ၑy;䐲ash»ံƀelr㧄㧒㧗ƀ;beⷪ㧋㧏ar;抻q;扚lip;拮Ābt㧜ᑨaòᑩr;쀀𝔳tré㦮suĀbp㧯㧱»ജ»൙pf;쀀𝕧roð໻tré㦴Ācu㨆㨋r;쀀𝓋Ābp㨐㨘nĀEe㦀㨖»㥾nĀEe㦒㨞»㦐igzag;榚΀cefoprs㨶㨻㩖㩛㩔㩡㩪irc;䅵Ādi㩀㩑Ābg㩅㩉ar;機eĀ;qᗺ㩏;扙erp;愘r;쀀𝔴pf;쀀𝕨Ā;eᑹ㩦atèᑹcr;쀀𝓌ૣណ㪇\0㪋\0㪐㪛\0\0㪝㪨㪫㪯\0\0㫃㫎\0㫘ៜ៟tré៑r;쀀𝔵ĀAa㪔㪗ròσrò৶;䎾ĀAa㪡㪤ròθrò৫að✓is;拻ƀdptឤ㪵㪾Āfl㪺ឩ;쀀𝕩imåឲĀAa㫇㫊ròώròਁĀcq㫒ីr;쀀𝓍Āpt៖㫜ré។Ѐacefiosu㫰㫽㬈㬌㬑㬕㬛㬡cĀuy㫶㫻te耻ý䃽;䑏Āiy㬂㬆rc;䅷;䑋n耻¥䂥r;쀀𝔶cy;䑗pf;쀀𝕪cr;쀀𝓎Ācm㬦㬩y;䑎l耻ÿ䃿Ԁacdefhiosw㭂㭈㭔㭘㭤㭩㭭㭴㭺㮀cute;䅺Āay㭍㭒ron;䅾;䐷ot;䅼Āet㭝㭡træᕟa;䎶r;쀀𝔷cy;䐶grarr;懝pf;쀀𝕫cr;쀀𝓏Ājn㮅㮇;怍j;怌'.split("").map(e=>e.charCodeAt(0))),ie=new Uint16Array("Ȁaglq\tɭ\0\0p;䀦os;䀧t;䀾t;䀼uot;䀢".split("").map(e=>e.charCodeAt(0)));let se=new Map([[0,65533],[128,8364],[130,8218],[131,402],[132,8222],[133,8230],[134,8224],[135,8225],[136,710],[137,8240],[138,352],[139,8249],[140,338],[142,381],[145,8216],[146,8217],[147,8220],[148,8221],[149,8226],[150,8211],[151,8212],[152,732],[153,8482],[154,353],[155,8250],[156,339],[158,382],[159,376]]),oe=null!=(x=String.fromCodePoint)?x:function(e){let t="";return 65535<e&&(e-=65536,t+=String.fromCharCode(e>>>10&1023|55296),e=56320|1023&e),t+=String.fromCharCode(e)};function ue(e){return e>=u.ZERO&&e<=u.NINE}(x=u=u||{})[x.NUM=35]="NUM",x[x.SEMI=59]="SEMI",x[x.EQUALS=61]="EQUALS",x[x.ZERO=48]="ZERO",x[x.NINE=57]="NINE",x[x.LOWER_A=97]="LOWER_A",x[x.LOWER_F=102]="LOWER_F",x[x.LOWER_X=120]="LOWER_X",x[x.LOWER_Z=122]="LOWER_Z",x[x.UPPER_A=65]="UPPER_A",x[x.UPPER_F=70]="UPPER_F",x[x.UPPER_Z=90]="UPPER_Z",(x=a=a||{})[x.VALUE_LENGTH=49152]="VALUE_LENGTH",x[x.BRANCH_LENGTH=16256]="BRANCH_LENGTH",x[x.JUMP_TABLE=127]="JUMP_TABLE",(x=o=o||{})[x.EntityStart=0]="EntityStart",x[x.NumericStart=1]="NumericStart",x[x.NumericDecimal=2]="NumericDecimal",x[x.NumericHex=3]="NumericHex",x[x.NamedEntity=4]="NamedEntity",(x=l=l||{})[x.Legacy=0]="Legacy",x[x.Strict=1]="Strict",x[x.Attribute=2]="Attribute";class ae{constructor(e,t,r){this.decodeTree=e,this.emitCodePoint=t,this.errors=r,this.state=o.EntityStart,this.consumed=1,this.result=0,this.treeIndex=0,this.excess=1,this.decodeMode=l.Strict}startEntity(e){this.decodeMode=e,this.state=o.EntityStart,this.result=0,this.treeIndex=0,this.excess=1,this.consumed=1}write(e,t){switch(this.state){case o.EntityStart:return e.charCodeAt(t)===u.NUM?(this.state=o.NumericStart,this.consumed+=1,this.stateNumericStart(e,t+1)):(this.state=o.NamedEntity,this.stateNamedEntity(e,t));case o.NumericStart:return this.stateNumericStart(e,t);case o.NumericDecimal:return this.stateNumericDecimal(e,t);case o.NumericHex:return this.stateNumericHex(e,t);case o.NamedEntity:return this.stateNamedEntity(e,t)}}stateNumericStart(e,t){return t>=e.length?-1:(32|e.charCodeAt(t))===u.LOWER_X?(this.state=o.NumericHex,this.consumed+=1,this.stateNumericHex(e,t+1)):(this.state=o.NumericDecimal,this.stateNumericDecimal(e,t))}addToNumericResult(e,t,r,n){t!==r&&(r=r-t,this.result=this.result*Math.pow(n,r)+parseInt(e.substr(t,r),n),this.consumed+=r)}stateNumericHex(e,t){for(var r,n=t;t<e.length;){var i=e.charCodeAt(t);if(!(ue(i)||(r=i)>=u.UPPER_A&&r<=u.UPPER_F||r>=u.LOWER_A&&r<=u.LOWER_F))return this.addToNumericResult(e,n,t,16),this.emitNumericEntity(i,3);t+=1}return this.addToNumericResult(e,n,t,16),-1}stateNumericDecimal(e,t){for(var r=t;t<e.length;){var n=e.charCodeAt(t);if(!ue(n))return this.addToNumericResult(e,r,t,10),this.emitNumericEntity(n,2);t+=1}return this.addToNumericResult(e,r,t,10),-1}emitNumericEntity(e,t){var r;if(this.consumed<=t)return null!=(t=this.errors)&&t.absenceOfDigitsInNumericCharacterReference(this.consumed),0;if(e===u.SEMI)this.consumed+=1;else if(this.decodeMode===l.Strict)return 0;return this.emitCodePoint(55296<=(t=this.result)&&t<=57343||1114111<t?65533:null!=(r=se.get(t))?r:t,this.consumed),this.errors&&(e!==u.SEMI&&this.errors.missingSemicolonAfterCharacterReference(),this.errors.validateNumericCharacterReference(this.result)),this.consumed}stateNamedEntity(e,t){var r,n=this.decodeTree;let i=n[this.treeIndex],s=(i&a.VALUE_LENGTH)>>14;for(;t<e.length;t++,this.excess++){var o=e.charCodeAt(t);if(this.treeIndex=function(r,e,t,n){var i=(e&a.BRANCH_LENGTH)>>7,s=e&a.JUMP_TABLE;if(0==i)return 0!=s&&n===s?t:-1;if(s){let e=n-s;return e<0||e>=i?-1:r[t+e]-1}let o=t,u=o+i-1;for(;o<=u;){let e=o+u>>>1,t=r[e];if(t<n)o=1+e;else{if(!(t>n))return r[e+i];u=e-1}}return-1}(n,i,this.treeIndex+Math.max(1,s),o),this.treeIndex<0)return 0===this.result||this.decodeMode===l.Attribute&&(0===s||(r=o)===u.EQUALS||r>=u.UPPER_A&&r<=u.UPPER_Z||r>=u.LOWER_A&&r<=u.LOWER_Z||ue(r))?0:this.emitNotTerminatedNamedEntity();if(i=n[this.treeIndex],0!==(s=(i&a.VALUE_LENGTH)>>14)){if(o===u.SEMI)return this.emitNamedEntityData(this.treeIndex,s,this.consumed+this.excess);this.decodeMode!==l.Strict&&(this.result=this.treeIndex,this.consumed+=this.excess,this.excess=0)}}return-1}emitNotTerminatedNamedEntity(){var{result:e,decodeTree:t}=this,t=(t[e]&a.VALUE_LENGTH)>>14;return this.emitNamedEntityData(e,t,this.consumed),null!=(e=this.errors)&&e.missingSemicolonAfterCharacterReference(),this.consumed}emitNamedEntityData(e,t,r){var n=this.decodeTree;return this.emitCodePoint(1===t?n[e]&~a.VALUE_LENGTH:n[e+1],r),3===t&&this.emitCodePoint(n[e+2],r),r}end(){var e;switch(this.state){case o.NamedEntity:return 0===this.result||this.decodeMode===l.Attribute&&this.result!==this.treeIndex?0:this.emitNotTerminatedNamedEntity();case o.NumericDecimal:return this.emitNumericEntity(0,2);case o.NumericHex:return this.emitNumericEntity(0,3);case o.NumericStart:return null!=(e=this.errors)&&e.absenceOfDigitsInNumericCharacterReference(this.consumed),0;case o.EntityStart:return 0}}}function le(e){let s="",o=new ae(e,e=>s+=oe(e));return function(t,r){let n=0,i=0;for(;0<=(i=t.indexOf("&",i));){s+=t.slice(n,i),o.startEntity(r);let e=o.write(t,i+1);if(e<0){n=i+o.end();break}n=i+e,i=0===e?n+1:n}let e=s+t.slice(n);return s="",e}}let ce=le(ne);function he(e,t=l.Legacy){return ce(e,t)}function pe(e){return"[object String]"===Object.prototype.toString.call(e)}le(ie);let fe=Object.prototype.hasOwnProperty;function c(r){return Array.prototype.slice.call(arguments,1).forEach(function(t){if(t){if("object"!=typeof t)throw new TypeError(t+"must be object");Object.keys(t).forEach(function(e){r[e]=t[e]})}}),r}function de(e,t,r){return[].concat(e.slice(0,t),r,e.slice(t+1))}function _e(e){return!(55296<=e&&e<=57343||64976<=e&&e<=65007||!(65535&~e&&65534!=(65535&e))||0<=e&&e<=8||11===e||14<=e&&e<=31||127<=e&&e<=159||1114111<e)}function h(e){var t;return 65535<e?(t=55296+((e-=65536)>>10),String.fromCharCode(t,56320+(1023&e))):String.fromCharCode(e)}let me=/\\([!"#$%&'()*+,\-./:;<=>?@[\\\]^_`{|}~])/g,ge=new RegExp(me.source+"|"+/&([a-z#][a-z0-9]{1,31});/gi.source,"gi"),ke=/^#((?:x[a-f0-9]{1,8}|[0-9]{1,8}))$/i;function p(e){return e.indexOf("\\")<0&&e.indexOf("&")<0?e:e.replace(ge,function(e,t,r){return t||function(t,r){if(35===r.charCodeAt(0)&&ke.test(r)){let e="x"===r[1].toLowerCase()?parseInt(r.slice(2),16):parseInt(r.slice(1),10);return _e(e)?h(e):t}let e=he(t);return e!==t?e:t}(e,r)})}let De=/[&<>"]/,Ce=/[&<>"]/g,ye={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;"};function Ee(e){return ye[e]}function f(e){return De.test(e)?e.replace(Ce,Ee):e}let Ae=/[.?*+^$[\]\\(){}|-]/g;function g(e){switch(e){case 9:case 32:return!0}return!1}function y(e){if(8192<=e&&e<=8202)return!0;switch(e){case 9:case 10:case 11:case 12:case 13:case 32:case 160:case 5760:case 8239:case 8287:case 12288:return!0}return!1}function E(e){return K.test(e)||ee.test(e)}function A(e){switch(e){case 33:case 34:case 35:case 36:case 37:case 38:case 39:case 40:case 41:case 42:case 43:case 44:case 45:case 46:case 47:case 58:case 59:case 60:case 61:case 62:case 63:case 64:case 91:case 92:case 93:case 94:case 95:case 96:case 123:case 124:case 125:case 126:return!0;default:return!1}}function _(e){return e=e.trim().replace(/\s+/g," "),(e="Ṿ"==="ẞ".toLowerCase()?e.replace(/\u1e9e/g,"ß"):e).toLowerCase().toUpperCase()}var Fe=Object.freeze({__proto__:null,arrayReplaceAt:de,assign:c,escapeHtml:f,escapeRE:function(e){return e.replace(Ae,"\\$&")},fromCodePoint:h,has:function(e,t){return fe.call(e,t)},isMdAsciiPunct:A,isPunctChar:E,isSpace:g,isString:pe,isValidEntityCode:_e,isWhiteSpace:y,lib:{mdurl:Q,ucmicro:re},normalizeReference:_,unescapeAll:p,unescapeMd:function(e){return e.indexOf("\\")<0?e:e.replace(me,"$1")}}),be=Object.freeze({__proto__:null,parseLinkDestination:function(t,r,n){let i,s=r;var o={ok:!1,pos:0,str:""};if(60===t.charCodeAt(s))for(s++;s<n;){if(10===(i=t.charCodeAt(s)))return o;if(60===i)return o;if(62===i)return o.pos=s+1,o.str=p(t.slice(r+1,s)),o.ok=!0,o;92===i&&s+1<n?s+=2:s++}else{let e=0;for(;s<n&&32!==(i=t.charCodeAt(s))&&!(i<32||127===i);)if(92===i&&s+1<n){if(32===t.charCodeAt(s+1))break;s+=2}else{if(40===i&&32<++e)return o;if(41===i){if(0===e)break;e--}s++}r!==s&&0===e&&(o.str=p(t.slice(r,s)),o.pos=s,o.ok=!0)}return o},parseLinkLabel:function(e,t,r){let n,i,s,o;var u=e.posMax,a=e.pos;for(e.pos=t+1,n=1;e.pos<u;){if(93===(s=e.src.charCodeAt(e.pos))&&0===--n){i=!0;break}if(o=e.pos,e.md.inline.skipToken(e),91===s)if(o===e.pos-1)n++;else if(r)return e.pos=a,-1}let l=-1;return i&&(l=e.pos),e.pos=a,l},parseLinkTitle:function(t,r,n,e){let i,s=r;var o={ok:!1,can_continue:!1,pos:0,str:"",marker:0};if(e)o.str=e.str,o.marker=e.marker;else{if(s>=n)return o;let e=t.charCodeAt(s);if(34!==e&&39!==e&&40!==e)return o;r++,s++,40===e&&(e=41),o.marker=e}for(;s<n;){if((i=t.charCodeAt(s))===o.marker)return o.pos=s+1,o.str+=p(t.slice(r,s)),o.ok=!0,o;if(40===i&&41===o.marker)return o;92===i&&s+1<n&&s++,s++}return o.can_continue=!0,o.str+=p(t.slice(r,s)),o}});let e={};function d(){this.rules=c({},e)}function t(){this.__rules__=[],this.__cache__=null}function m(e,t,r){this.type=e,this.tag=t,this.attrs=null,this.map=null,this.nesting=r,this.level=0,this.children=null,this.content="",this.markup="",this.info="",this.meta=null,this.block=!1,this.hidden=!1}function ve(e,t,r){this.src=e,this.env=r,this.tokens=[],this.inlineMode=!1,this.md=t}e.code_inline=function(e,t,r,n,i){e=e[t];return"<code"+i.renderAttrs(e)+">"+f(e.content)+"</code>"},e.code_block=function(e,t,r,n,i){var s=e[t];return"<pre"+i.renderAttrs(s)+"><code>"+f(e[t].content)+"</code></pre>\n"},e.fence=function(e,t,n,r,i){var s=e[t],t=s.info?p(s.info).trim():"";let o,u="",a="";if(t){let e=t.split(/(\s+)/g);u=e[0],a=e.slice(2).join("")}if(0===(o=n.highlight&&n.highlight(s.content,u,a)||f(s.content)).indexOf("<pre"))return o+"\n";if(t){let e=s.attrIndex("class"),t=s.attrs?s.attrs.slice():[],r=(e<0?t.push(["class",n.langPrefix+u]):(t[e]=t[e].slice(),t[e][1]+=" "+n.langPrefix+u),{attrs:t});return`<pre><code${i.renderAttrs(r)}>${o}</code></pre>
`}return`<pre><code${i.renderAttrs(s)}>${o}</code></pre>
`},e.image=function(e,t,r,n,i){var s=e[t];return s.attrs[s.attrIndex("alt")][1]=i.renderInlineAsText(s.children,r,n),i.renderToken(e,t,r)},e.hardbreak=function(e,t,r){return r.xhtmlOut?"<br />\n":"<br>\n"},e.softbreak=function(e,t,r){return r.breaks?r.xhtmlOut?"<br />\n":"<br>\n":"\n"},e.text=function(e,t){return f(e[t].content)},e.html_block=function(e,t){return e[t].content},e.html_inline=function(e,t){return e[t].content},d.prototype.renderAttrs=function(e){let t,r,n;if(!e.attrs)return"";for(n="",t=0,r=e.attrs.length;t<r;t++)n+=" "+f(e.attrs[t][0])+'="'+f(e.attrs[t][1])+'"';return n},d.prototype.renderToken=function(t,r,e){var n=t[r];let i="";if(n.hidden)return"";n.block&&-1!==n.nesting&&r&&t[r-1].hidden&&(i+="\n"),i=(i+=(-1===n.nesting?"</":"<")+n.tag)+this.renderAttrs(n),0===n.nesting&&e.xhtmlOut&&(i+=" /");let s=!1;if(n.block&&(s=!0,1===n.nesting)&&r+1<t.length){let e=t[r+1];("inline"===e.type||e.hidden||-1===e.nesting&&e.tag===n.tag)&&(s=!1)}return i+=s?">\n":">"},d.prototype.renderInline=function(r,n,i){let s="";var o=this.rules;for(let e=0,t=r.length;e<t;e++){var u=r[e].type;void 0!==o[u]?s+=o[u](r,e,n,i,this):s+=this.renderToken(r,e,n)}return s},d.prototype.renderInlineAsText=function(r,n,i){let s="";for(let e=0,t=r.length;e<t;e++)switch(r[e].type){case"text":case"html_inline":case"html_block":s+=r[e].content;break;case"image":s+=this.renderInlineAsText(r[e].children,n,i);break;case"softbreak":case"hardbreak":s+="\n"}return s},d.prototype.render=function(r,n,i){let s="";var o=this.rules;for(let e=0,t=r.length;e<t;e++){var u=r[e].type;"inline"===u?s+=this.renderInline(r[e].children,n,i):void 0!==o[u]?s+=o[u](r,e,n,i,this):s+=this.renderToken(r,e,n,i)}return s},t.prototype.__find__=function(t){for(let e=0;e<this.__rules__.length;e++)if(this.__rules__[e].name===t)return e;return-1},t.prototype.__compile__=function(){let r=this,t=[""];r.__rules__.forEach(function(e){e.enabled&&e.alt.forEach(function(e){t.indexOf(e)<0&&t.push(e)})}),r.__cache__={},t.forEach(function(t){r.__cache__[t]=[],r.__rules__.forEach(function(e){!e.enabled||t&&e.alt.indexOf(t)<0||r.__cache__[t].push(e.fn)})})},t.prototype.at=function(e,t,r){var n=this.__find__(e),r=r||{};if(-1===n)throw new Error("Parser rule not found: "+e);this.__rules__[n].fn=t,this.__rules__[n].alt=r.alt||[],this.__cache__=null},t.prototype.before=function(e,t,r,n){var i=this.__find__(e),n=n||{};if(-1===i)throw new Error("Parser rule not found: "+e);this.__rules__.splice(i,0,{name:t,enabled:!0,fn:r,alt:n.alt||[]}),this.__cache__=null},t.prototype.after=function(e,t,r,n){var i=this.__find__(e),n=n||{};if(-1===i)throw new Error("Parser rule not found: "+e);this.__rules__.splice(i+1,0,{name:t,enabled:!0,fn:r,alt:n.alt||[]}),this.__cache__=null},t.prototype.push=function(e,t,r){this.__rules__.push({name:e,enabled:!0,fn:t,alt:(r||{}).alt||[]}),this.__cache__=null},t.prototype.enable=function(e,r){Array.isArray(e)||(e=[e]);let n=[];return e.forEach(function(e){var t=this.__find__(e);if(t<0){if(r)return;throw new Error("Rules manager: invalid rule name "+e)}this.__rules__[t].enabled=!0,n.push(e)},this),this.__cache__=null,n},t.prototype.enableOnly=function(e,t){Array.isArray(e)||(e=[e]),this.__rules__.forEach(function(e){e.enabled=!1}),this.enable(e,t)},t.prototype.disable=function(e,r){Array.isArray(e)||(e=[e]);let n=[];return e.forEach(function(e){var t=this.__find__(e);if(t<0){if(r)return;throw new Error("Rules manager: invalid rule name "+e)}this.__rules__[t].enabled=!1,n.push(e)},this),this.__cache__=null,n},t.prototype.getRules=function(e){return null===this.__cache__&&this.__compile__(),this.__cache__[e]||[]},m.prototype.attrIndex=function(r){if(this.attrs){var n=this.attrs;for(let e=0,t=n.length;e<t;e++)if(n[e][0]===r)return e}return-1},m.prototype.attrPush=function(e){this.attrs?this.attrs.push(e):this.attrs=[e]},m.prototype.attrSet=function(e,t){var r=this.attrIndex(e),e=[e,t];r<0?this.attrPush(e):this.attrs[r]=e},m.prototype.attrGet=function(e){e=this.attrIndex(e);let t=null;return t=0<=e?this.attrs[e][1]:t},m.prototype.attrJoin=function(e,t){var r=this.attrIndex(e);r<0?this.attrPush([e,t]):this.attrs[r][1]=this.attrs[r][1]+" "+t},ve.prototype.Token=m;let xe=/\r\n?|\n/g,we=/\0/g;let ze=/\+-|\.\.|\?\?\?\?|!!!!|,,|--/,Se=/\((c|tm|r)\)/i,qe=/\((c|tm|r)\)/gi,Be={c:"©",r:"®",tm:"™"};function Le(e,t){return Be[t.toLowerCase()]}let Ie=/['"]/,Me=/['"]/g;function F(e,t,r){return e.slice(0,t)+r+e.slice(t+1)}let Te=[["normalize",function(e){let t;t=(t=e.src.replace(xe,"\n")).replace(we,"�"),e.src=t}],["block",function(e){var t;e.inlineMode?((t=new e.Token("inline","",0)).content=e.src,t.map=[0,1],t.children=[],e.tokens.push(t)):e.md.block.parse(e.src,e.md,e.env,e.tokens)}],["inline",function(r){var n=r.tokens;for(let e=0,t=n.length;e<t;e++){var i=n[e];"inline"===i.type&&r.md.inline.parse(i.content,r.md,r.env,i.children)}}],["linkify",function(u){let a=u.tokens;var r;if(u.md.options.linkify)for(let o=0,e=a.length;o<e;o++)if("inline"===a[o].type&&u.md.linkify.pretest(a[o].content)){let s=a[o].children,t=0;for(let e=s.length-1;0<=e;e--){var l=s[e];if("link_close"!==l.type){if("html_inline"===l.type&&(/^<a[>\s]/i.test(r=l.content)&&0<t&&t--,/^<\/a\s*>/i.test(l.content))&&t++,!(0<t)&&"text"===l.type&&u.md.linkify.test(l.content)){let t=l.content,r=u.md.linkify.match(t);var c=[];let n=l.level,i=0;0<r.length&&0===r[0].index&&0<e&&"text_special"===s[e-1].type&&(r=r.slice(1));for(let e=0;e<r.length;e++){var h=r[e].url,h=u.md.normalizeLink(h);if(u.md.validateLink(h)){var p=r[e].text,p=r[e].schema?"mailto:"!==r[e].schema||/^mailto:/i.test(p)?u.md.normalizeLinkText(p):u.md.normalizeLinkText("mailto:"+p).replace(/^mailto:/,""):u.md.normalizeLinkText("http://"+p).replace(/^http:\/\//,""),f=r[e].index;if(f>i){let e=new u.Token("text","",0);e.content=t.slice(i,f),e.level=n,c.push(e)}f=new u.Token("link_open","a",1),h=(f.attrs=[["href",h]],f.level=n++,f.markup="linkify",f.info="auto",c.push(f),new u.Token("text","",0)),f=(h.content=p,h.level=n,c.push(h),new u.Token("link_close","a",-1));f.level=--n,f.markup="linkify",f.info="auto",c.push(f),i=r[e].lastIndex}}if(i<t.length){let e=new u.Token("text","",0);e.content=t.slice(i),e.level=n,c.push(e)}a[o].children=s=de(s,e,c)}}else for(e--;s[e].level!==l.level&&"link_open"!==s[e].type;)e--}}}],["replacements",function(e){let r;if(e.md.options.typographer)for(r=e.tokens.length-1;0<=r;r--)if("inline"===e.tokens[r].type&&(Se.test(e.tokens[r].content)&&function(t){let r=0;for(let e=t.length-1;0<=e;e--){var n=t[e];"text"!==n.type||r||(n.content=n.content.replace(qe,Le)),"link_open"===n.type&&"auto"===n.info&&r--,"link_close"===n.type&&"auto"===n.info&&r++}}(e.tokens[r].children),ze.test(e.tokens[r].content))){n=void 0;i=void 0;var n=e.tokens[r].children;let t=0;for(let e=n.length-1;0<=e;e--){var i=n[e];"text"!==i.type||t||ze.test(i.content)&&(i.content=i.content.replace(/\+-/g,"±").replace(/\.{2,}/g,"…").replace(/([?!])\u2026/g,"$1..").replace(/([?!]){4,}/g,"$1$1$1").replace(/,{2,}/g,",").replace(/(^|[^-])---(?=[^-]|$)/gm,"$1—").replace(/(^|\s)--(?=\s|$)/gm,"$1–").replace(/(^|[^-\s])--(?=[^-\s]|$)/gm,"$1–")),"link_open"===i.type&&"auto"===i.info&&t--,"link_close"===i.type&&"auto"===i.info&&t++}}}],["smartquotes",function(t){if(t.md.options.typographer)for(let e=t.tokens.length-1;0<=e;e--)if("inline"===t.tokens[e].type&&Ie.test(t.tokens[e].content)){l=void 0;c=void 0;h=void 0;p=void 0;f=void 0;d=void 0;_=void 0;m=void 0;g=void 0;k=void 0;D=void 0;C=void 0;var l=t.tokens[e].children;var c=t;let a;var h=[];for(let u=0;u<l.length;u++){var p=l[u],f=l[u].level;for(a=h.length-1;0<=a&&!(h[a].level<=f);a--);if(h.length=a+1,"text"===p.type){let i=p.content,s=0,o=i.length;e:for(;s<o;){Me.lastIndex=s;var d=Me.exec(i);if(!d)break;let e=!0,t=!0;s=d.index+1;var _="'"===d[0];let r=32;if(0<=d.index-1)r=i.charCodeAt(d.index-1);else for(a=u-1;0<=a&&"softbreak"!==l[a].type&&"hardbreak"!==l[a].type;a--)if(l[a].content){r=l[a].content.charCodeAt(l[a].content.length-1);break}let n=32;if(s<o)n=i.charCodeAt(s);else for(a=u+1;a<l.length&&"softbreak"!==l[a].type&&"hardbreak"!==l[a].type;a++)if(l[a].content){n=l[a].content.charCodeAt(0);break}var m=A(r)||E(String.fromCharCode(r)),g=A(n)||E(String.fromCharCode(n)),k=y(r),D=y(n);if(D?e=!1:!g||k||m||(e=!1),k?t=!1:!m||D||g||(t=!1),34===n&&'"'===d[0]&&48<=r&&r<=57&&(t=e=!1),e&&t&&(e=m,t=g),e||t){if(t)for(a=h.length-1;0<=a;a--){var C=h[a];if(h[a].level<f)break;if(C.single===_&&h[a].level===f){let e,t;C=h[a],t=_?(e=c.md.options.quotes[2],c.md.options.quotes[3]):(e=c.md.options.quotes[0],c.md.options.quotes[1]),p.content=F(p.content,d.index,t),l[C.token].content=F(l[C.token].content,C.pos,e),s+=t.length-1,C.token===u&&(s+=e.length-1),i=p.content,o=i.length,h.length=a;continue e}}e?h.push({token:u,pos:d.index,single:_,level:f}):t&&_&&(p.content=F(p.content,d.index,"’"))}else _&&(p.content=F(p.content,d.index,"’"))}}}}}],["text_join",function(e){let n,i,s=e.tokens,t=s.length;for(let r=0;r<t;r++)if("inline"===s[r].type){let e=s[r].children,t=e.length;for(n=0;n<t;n++)"text_special"===e[n].type&&(e[n].type="text");for(n=i=0;n<t;n++)"text"===e[n].type&&n+1<t&&"text"===e[n+1].type?e[n+1].content=e[n].content+e[n+1].content:(n!==i&&(e[i]=e[n]),i++);n!==i&&(e.length=i)}}]];function Re(){this.ruler=new t;for(let e=0;e<Te.length;e++)this.ruler.push(Te[e][0],Te[e][1])}function k(e,t,r,n){this.src=e,this.md=t,this.env=r,this.tokens=n,this.bMarks=[],this.eMarks=[],this.tShift=[],this.sCount=[],this.bsCount=[],this.blkIndent=0,this.line=0,this.lineMax=0,this.tight=!1,this.ddIndent=-1,this.listIndent=-1,this.parentType="root",this.level=0;var o=this.src;for(let e=0,t=0,r=0,n=0,i=o.length,s=!1;t<i;t++){var u=o.charCodeAt(t);if(!s){if(g(u)){r++,9===u?n+=4-n%4:n++;continue}s=!0}10!==u&&t!==i-1||(10!==u&&t++,this.bMarks.push(e),this.eMarks.push(t),this.tShift.push(r),this.sCount.push(n),this.bsCount.push(0),s=!1,r=0,n=0,e=t+1)}this.bMarks.push(o.length),this.eMarks.push(o.length),this.tShift.push(0),this.sCount.push(0),this.bsCount.push(0),this.lineMax=this.bMarks.length-1}function Ne(e,t){var r=e.bMarks[t]+e.tShift[t],t=e.eMarks[t];return e.src.slice(r,t)}function Pe(e){var t=[],r=e.length;let n=0,i=e.charCodeAt(n),s=!1,o=0,u="";for(;n<r;)124===i&&(o=s?(u+=e.substring(o,n-1),n):(t.push(u+e.substring(o,n)),u="",n+1)),s=92===i,n++,i=e.charCodeAt(n);return t.push(u+e.substring(o)),t}function Oe(e,t){var r=e.eMarks[t],t=e.bMarks[t]+e.tShift[t],n=e.src.charCodeAt(t++);return 42!==n&&45!==n&&43!==n||t<r&&!g(e.src.charCodeAt(t))?-1:t}function je(e,t){var r=e.bMarks[t]+e.tShift[t],n=e.eMarks[t];let i=r;if(i+1>=n)return-1;let s=e.src.charCodeAt(i++);if(s<48||57<s)return-1;for(;;){if(i>=n)return-1;if(!(48<=(s=e.src.charCodeAt(i++))&&s<=57)){if(41===s||46===s)break;return-1}if(10<=i-r)return-1}return i<n&&!g(s=e.src.charCodeAt(i))?-1:i}Re.prototype.process=function(r){var n=this.ruler.getRules("");for(let e=0,t=n.length;e<t;e++)n[e](r)},Re.prototype.State=ve,k.prototype.push=function(e,t,r){e=new m(e,t,r);return e.block=!0,r<0&&this.level--,e.level=this.level,0<r&&this.level++,this.tokens.push(e),e},k.prototype.isEmpty=function(e){return this.bMarks[e]+this.tShift[e]>=this.eMarks[e]},k.prototype.skipEmptyLines=function(e){for(var t=this.lineMax;e<t&&!(this.bMarks[e]+this.tShift[e]<this.eMarks[e]);e++);return e},k.prototype.skipSpaces=function(e){for(var t=this.src.length;e<t&&g(this.src.charCodeAt(e));e++);return e},k.prototype.skipSpacesBack=function(e,t){if(!(e<=t))for(;t<e;)if(!g(this.src.charCodeAt(--e)))return e+1;return e},k.prototype.skipChars=function(e,t){for(var r=this.src.length;e<r&&this.src.charCodeAt(e)===t;e++);return e},k.prototype.skipCharsBack=function(e,t,r){if(!(e<=r))for(;r<e;)if(t!==this.src.charCodeAt(--e))return e+1;return e},k.prototype.getLines=function(e,s,o,u){if(s<=e)return"";var a=new Array(s-e);for(let n=0,i=e;i<s;i++,n++){let t=0;var l=this.bMarks[i];let e,r=l;for(e=i+1<s||u?this.eMarks[i]+1:this.eMarks[i];r<e&&t<o;){let e=this.src.charCodeAt(r);if(g(e))9===e?t+=4-(t+this.bsCount[i])%4:t++;else{if(!(r-l<this.tShift[i]))break;t++}r++}a[n]=t>o?new Array(t-o+1).join(" ")+this.src.slice(r,e):this.src.slice(r,e)}return a.join("")},k.prototype.Token=m;let Ze="<[A-Za-z][A-Za-z0-9\\-]*(?:\\s+[a-zA-Z_:][a-zA-Z0-9:._-]*(?:\\s*=\\s*(?:[^\"'=<>`\\x00-\\x20]+|'[^']*'|\"[^\"]*\"))?)*\\s*\\/?>",$e="<\\/[A-Za-z][A-Za-z0-9\\-]*\\s*>",Ue=new RegExp("^(?:"+Ze+"|"+$e+"|\x3c!---?>|\x3c!--(?:[^-]|-[^-]|--[^>])*--\x3e|<[?][\\s\\S]*?[?]>|<![A-Za-z][^>]*>|<!\\[CDATA\\[[\\s\\S]*?\\]\\]>)"),He=new RegExp("^(?:"+Ze+"|"+$e+")"),D=[[/^<(script|pre|style|textarea)(?=(\s|>|$))/i,/<\/(script|pre|style|textarea)>/i,!0],[/^<!--/,/-->/,!0],[/^<\?/,/\?>/,!0],[/^<![A-Z]/,/>/,!0],[/^<!\[CDATA\[/,/\]\]>/,!0],[new RegExp("^</?("+["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","search","section","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"].join("|")+")(?=(\\s|/?>|$))","i"),/^$/,!0],[new RegExp(He.source+"\\s*$"),/^$/,!1]],C=[["table",function(n,i,s,e){if(s<i+2)return!1;let o=i+1;if(n.sCount[o]<n.blkIndent)return!1;if(4<=n.sCount[o]-n.blkIndent)return!1;let t=n.bMarks[o]+n.tShift[o];if(t>=n.eMarks[o])return!1;var r=n.src.charCodeAt(t++);if(124!==r&&45!==r&&58!==r)return!1;if(t>=n.eMarks[o])return!1;var u=n.src.charCodeAt(t++);if(124!==u&&45!==u&&58!==u&&!g(u))return!1;if(45===r&&g(u))return!1;for(;t<n.eMarks[o];){let e=n.src.charCodeAt(t);if(124!==e&&45!==e&&58!==e&&!g(e))return!1;t++}let a=Ne(n,i+1),l=a.split("|");var c=[];for(let t=0;t<l.length;t++){let e=l[t].trim();if(!e){if(0===t||t===l.length-1)continue;return!1}if(!/^:?-+:?$/.test(e))return!1;58===e.charCodeAt(e.length-1)?c.push(58===e.charCodeAt(0)?"center":"right"):58===e.charCodeAt(0)?c.push("left"):c.push("")}if(-1===(a=Ne(n,i).trim()).indexOf("|"))return!1;if(4<=n.sCount[i]-n.blkIndent)return!1;(l=Pe(a)).length&&""===l[0]&&l.shift(),l.length&&""===l[l.length-1]&&l.pop();var h=l.length;if(0===h||h!==c.length)return!1;if(!e){var r=n.parentType,p=(n.parentType="table",n.md.block.ruler.getRules("blockquote")),u=[i,0];n.push("table_open","table",1).map=u,n.push("thead_open","thead",1).map=[i,i+1],n.push("tr_open","tr",1).map=[i,i+1];for(let r=0;r<l.length;r++){let e=n.push("th_open","th",1),t=(c[r]&&(e.attrs=[["style","text-align:"+c[r]]]),n.push("inline","",0));t.content=l[r].trim(),t.children=[],n.push("th_close","th",-1)}let e,t=(n.push("tr_close","tr",-1),n.push("thead_close","thead",-1),0);for(o=i+2;o<s&&!(n.sCount[o]<n.blkIndent);o++){let r=!1;for(let e=0,t=p.length;e<t;e++)if(p[e](n,o,s,!0)){r=!0;break}if(r)break;if(!(a=Ne(n,o).trim()))break;if(4<=n.sCount[o]-n.blkIndent)break;if((l=Pe(a)).length&&""===l[0]&&l.shift(),l.length&&""===l[l.length-1]&&l.pop(),65536<(t+=h-l.length))break;o===i+2&&(n.push("tbody_open","tbody",1).map=e=[i+2,0]),n.push("tr_open","tr",1).map=[o,o+1];for(let r=0;r<h;r++){let e=n.push("td_open","td",1),t=(c[r]&&(e.attrs=[["style","text-align:"+c[r]]]),n.push("inline","",0));t.content=l[r]?l[r].trim():"",t.children=[],n.push("td_close","td",-1)}n.push("tr_close","tr",-1)}e&&(n.push("tbody_close","tbody",-1),e[1]=o),n.push("table_close","table",-1),u[1]=o,n.parentType=r,n.line=o}return!0},["paragraph","reference"]],["code",function(e,t,r){if(e.sCount[t]-e.blkIndent<4)return!1;let n=t+1,i=n;for(;n<r;)if(e.isEmpty(n))n++;else{if(!(4<=e.sCount[n]-e.blkIndent))break;n++,i=n}e.line=i;var s=e.push("code_block","code",0);return s.content=e.getLines(t,i,4+e.blkIndent,!1)+"\n",s.map=[t,e.line],!0}],["fence",function(r,n,i,s){let o=r.bMarks[n]+r.tShift[n],u=r.eMarks[n];if(4<=r.sCount[n]-r.blkIndent)return!1;if(o+3>u)return!1;var a=r.src.charCodeAt(o);if(126!==a&&96!==a)return!1;let l=o,c=(o=r.skipChars(o,a))-l;if(c<3)return!1;var h=r.src.slice(l,o),p=r.src.slice(o,u);if(96===a&&0<=p.indexOf(String.fromCharCode(a)))return!1;if(!s){let e=n,t=!1;for(;!(++e>=i||(o=l=r.bMarks[e]+r.tShift[e],u=r.eMarks[e],o<u&&r.sCount[e]<r.blkIndent));)if(r.src.charCodeAt(o)===a&&!(4<=r.sCount[e]-r.blkIndent||(o=r.skipChars(o,a))-l<c||(o=r.skipSpaces(o))<u)){t=!0;break}c=r.sCount[n],r.line=e+(t?1:0);s=r.push("fence","code",0);s.info=p,s.content=r.getLines(n+1,e,c,!0),s.markup=h,s.map=[n,r.line]}return!0},["paragraph","reference","blockquote","list"]],["blockquote",function(o,t,n,e){let u=o.bMarks[t]+o.tShift[t],a=void o.eMarks[t];var r=o.lineMax;if(4<=o.sCount[t]-o.blkIndent)return!1;if(62!==o.src.charCodeAt(u))return!1;if(!e){var l=[],c=[],h=[],p=[],f=o.md.block.ruler.getRules("blockquote"),e=o.parentType;let i,s=!(o.parentType="blockquote");for(i=t;i<n;i++){let e=o.sCount[i]<o.blkIndent;if(u=o.bMarks[i]+o.tShift[i],a=o.eMarks[i],u>=a)break;if(62!==o.src.charCodeAt(u++)||e){if(s)break;let r=!1;for(let e=0,t=f.length;e<t;e++)if(f[e](o,i,n,!0)){r=!0;break}if(r){o.lineMax=i,0!==o.blkIndent&&(l.push(o.bMarks[i]),c.push(o.bsCount[i]),p.push(o.tShift[i]),h.push(o.sCount[i]),o.sCount[i]-=o.blkIndent);break}l.push(o.bMarks[i]),c.push(o.bsCount[i]),p.push(o.tShift[i]),h.push(o.sCount[i]),o.sCount[i]=-1}else{let e,t,r=o.sCount[i]+1,n=(32===o.src.charCodeAt(u)?(u++,r++,t=!1,e=!0):9===o.src.charCodeAt(u)?(e=!0,t=(o.bsCount[i]+r)%4!=3||(u++,r++,!1)):e=!1,r);for(l.push(o.bMarks[i]),o.bMarks[i]=u;u<a;){let e=o.src.charCodeAt(u);if(!g(e))break;9===e?n+=4-(n+o.bsCount[i]+(t?1:0))%4:n++,u++}s=u>=a,c.push(o.bsCount[i]),o.bsCount[i]=o.sCount[i]+1+(e?1:0),h.push(o.sCount[i]),o.sCount[i]=n-r,p.push(o.tShift[i]),o.tShift[i]=u-o.bMarks[i]}}var d=o.blkIndent,_=(o.blkIndent=0,o.push("blockquote_open","blockquote",1)),m=(_.markup=">",[t,0]);_.map=m,o.md.block.tokenize(o,t,i),o.push("blockquote_close","blockquote",-1).markup=">",o.lineMax=r,o.parentType=e,m[1]=o.line;for(let e=0;e<p.length;e++)o.bMarks[e+t]=l[e],o.tShift[e+t]=p[e],o.sCount[e+t]=h[e],o.bsCount[e+t]=c[e];o.blkIndent=d}return!0},["paragraph","reference","blockquote","list"]],["hr",function(t,e,r,n){var i=t.eMarks[e];if(4<=t.sCount[e]-t.blkIndent)return!1;let s=t.bMarks[e]+t.tShift[e];var o=t.src.charCodeAt(s++);if(42!==o&&45!==o&&95!==o)return!1;let u=1;for(;s<i;){let e=t.src.charCodeAt(s++);if(e!==o&&!g(e))return!1;e===o&&u++}return!(u<3||(n||(t.line=e+1,(n=t.push("hr","hr",0)).map=[e,t.line],n.markup=Array(u+1).join(String.fromCharCode(o))),0))},["paragraph","reference","blockquote","list"]],["list",function(d,n,_,e){let m,g,k,D,C=n,y=!0;if(4<=d.sCount[C]-d.blkIndent)return!1;if(0<=d.listIndent&&4<=d.sCount[C]-d.listIndent&&d.sCount[C]<d.blkIndent)return!1;let E,i,A,t=!1;if(e&&"paragraph"===d.parentType&&d.sCount[C]>=d.blkIndent&&(t=!0),0<=(A=je(d,C))){if(E=!0,k=d.bMarks[C]+d.tShift[C],i=Number(d.src.slice(k,A-1)),t&&1!==i)return!1}else{if(!(0<=(A=Oe(d,C))))return!1;E=!1}if(t&&d.skipSpaces(A)>=d.eMarks[C])return!1;if(!e){let h=d.src.charCodeAt(A-1),e=d.tokens.length,t=(E?(D=d.push("ordered_list_open","ol",1),1!==i&&(D.attrs=[["start",i]])):D=d.push("bullet_list_open","ul",1),[C,0]),p=(D.map=t,D.markup=String.fromCharCode(h),!1),f=d.md.block.ruler.getRules("list"),r=d.parentType;for(d.parentType="list";C<_;){g=A,m=d.eMarks[C];let e=d.sCount[C]+A-(d.bMarks[C]+d.tShift[C]),t=e;for(;g<m;){let e=d.src.charCodeAt(g);if(9===e)t+=4-(t+d.bsCount[C])%4;else{if(32!==e)break;t++}g++}let r=g,n,i=e+(n=4<(n=r>=m?1:t-e)?1:n),s=((D=d.push("list_item_open","li",1)).markup=String.fromCharCode(h),[C,0]),o=(D.map=s,E&&(D.info=d.src.slice(k,A-1)),d.tight),u=d.tShift[C],a=d.sCount[C],l=d.listIndent;if(d.listIndent=d.blkIndent,d.blkIndent=i,d.tight=!0,d.tShift[C]=r-d.bMarks[C],d.sCount[C]=t,r>=m&&d.isEmpty(C+1)?d.line=Math.min(d.line+2,_):d.md.block.tokenize(d,C,_,!0),d.tight&&!p||(y=!1),p=1<d.line-C&&d.isEmpty(d.line-1),d.blkIndent=d.listIndent,d.listIndent=l,d.tShift[C]=u,d.sCount[C]=a,d.tight=o,(D=d.push("list_item_close","li",-1)).markup=String.fromCharCode(h),C=d.line,(s[1]=C)>=_)break;if(d.sCount[C]<d.blkIndent)break;if(4<=d.sCount[C]-d.blkIndent)break;let c=!1;for(let e=0,t=f.length;e<t;e++)if(f[e](d,C,_,!0)){c=!0;break}if(c)break;if(E){if((A=je(d,C))<0)break;k=d.bMarks[C]+d.tShift[C]}else if((A=Oe(d,C))<0)break;if(h!==d.src.charCodeAt(A-1))break}if((D=E?d.push("ordered_list_close","ol",-1):d.push("bullet_list_close","ul",-1)).markup=String.fromCharCode(h),t[1]=C,d.line=C,d.parentType=r,y){var s=d;n=e;var o=s.level+2;for(let e=n+2,t=s.tokens.length-2;e<t;e++)s.tokens[e].level===o&&"paragraph_open"===s.tokens[e].type&&(s.tokens[e+2].hidden=!0,s.tokens[e].hidden=!0,e+=2)}}return!0},["paragraph","reference","blockquote"]],["reference",function(o,e,t,r){let n=o.bMarks[e]+o.tShift[e],i=o.eMarks[e],s=e+1;if(4<=o.sCount[e]-o.blkIndent)return!1;if(91!==o.src.charCodeAt(n))return!1;function u(i){var s=o.lineMax;if(s<=i||o.isEmpty(i))return null;let e=!1;if(3<o.sCount[i]-o.blkIndent&&(e=!0),!(e=o.sCount[i]<0?!0:e)){let r=o.md.block.ruler.getRules("reference"),e=o.parentType,n=!(o.parentType="reference");for(let e=0,t=r.length;e<t;e++)if(r[e](o,i,s,!0)){n=!0;break}if(o.parentType=e,n)return null}let t=o.bMarks[i]+o.tShift[i],r=o.eMarks[i];return o.src.slice(t,r+1)}let a=o.src.slice(n,i+1),l=(i=a.length,-1);for(n=1;n<i;n++){let e=a.charCodeAt(n);if(91===e)return!1;if(93===e){l=n;break}if(10===e){let e=u(s);null!==e&&(a+=e,i=a.length,s++)}else if(92===e&&++n<i&&10===a.charCodeAt(n)){let e=u(s);null!==e&&(a+=e,i=a.length,s++)}}if(l<0||58!==a.charCodeAt(l+1))return!1;for(n=l+2;n<i;n++){let e=a.charCodeAt(n);if(10===e){let e=u(s);null!==e&&(a+=e,i=a.length,s++)}else if(!g(e))break}e=o.md.helpers.parseLinkDestination(a,n,i);if(!e.ok)return!1;var c=o.md.normalizeLink(e.str);if(!o.md.validateLink(c))return!1;for(var e=n=e.pos,h=s,p=n;n<i;n++){let e=a.charCodeAt(n);if(10===e){let e=u(s);null!==e&&(a+=e,i=a.length,s++)}else if(!g(e))break}let f,d=o.md.helpers.parseLinkTitle(a,n,i);for(;d.can_continue;){let e=u(s);if(null===e)break;a+=e,n=i,i=a.length,s++,d=o.md.helpers.parseLinkTitle(a,n,i,d)}for(n<i&&p!==n&&d.ok?(f=d.str,n=d.pos):(f="",n=e,s=h);n<i&&g(a.charCodeAt(n));)n++;if(n<i&&10!==a.charCodeAt(n)&&f)for(f="",n=e,s=h;n<i&&g(a.charCodeAt(n));)n++;return!(n<i&&10!==a.charCodeAt(n)||!(p=_(a.slice(1,l)))||(r||(void 0===o.env.references&&(o.env.references={}),void 0===o.env.references[p]&&(o.env.references[p]={title:f,href:c}),o.line=s),0))}],["html_block",function(e,t,r,n){var i=e.bMarks[t]+e.tShift[t],s=e.eMarks[t];if(4<=e.sCount[t]-e.blkIndent)return!1;if(!e.md.options.html)return!1;if(60!==e.src.charCodeAt(i))return!1;let o=e.src.slice(i,s),u=0;for(;u<D.length&&!D[u][0].test(o);u++);if(u===D.length)return!1;if(n)return D[u][2];let a=t+1;if(!D[u][1].test(o))for(;a<r&&!(e.sCount[a]<e.blkIndent);a++)if(i=e.bMarks[a]+e.tShift[a],s=e.eMarks[a],o=e.src.slice(i,s),D[u][1].test(o)){0!==o.length&&a++;break}e.line=a;n=e.push("html_block","",0);return n.map=[t,a],n.content=e.getLines(t,a,e.blkIndent,!0),!0},["paragraph","reference","blockquote"]],["heading",function(e,t,r,n){let i=e.bMarks[t]+e.tShift[t],s=e.eMarks[t];if(4<=e.sCount[t]-e.blkIndent)return!1;let o=e.src.charCodeAt(i);if(35!==o||i>=s)return!1;let u=1;for(o=e.src.charCodeAt(++i);35===o&&i<s&&u<=6;)u++,o=e.src.charCodeAt(++i);return!(6<u||i<s&&!g(o)||(n||(s=e.skipSpacesBack(s,i),(n=e.skipCharsBack(s,35,i))>i&&g(e.src.charCodeAt(n-1))&&(s=n),e.line=t+1,(n=e.push("heading_open","h"+String(u),1)).markup="########".slice(0,u),n.map=[t,e.line],(n=e.push("inline","",0)).content=e.src.slice(i,s).trim(),n.map=[t,e.line],n.children=[],e.push("heading_close","h"+String(u),-1).markup="########".slice(0,u)),0))},["paragraph","reference","blockquote"]],["lheading",function(n,e,i){var s=n.md.block.ruler.getRules("paragraph");if(4<=n.sCount[e]-n.blkIndent)return!1;var t,r,o=n.parentType;n.parentType="paragraph";let u,a=0,l=e+1;for(;l<i&&!n.isEmpty(l);l++)if(!(3<n.sCount[l]-n.blkIndent)){if(n.sCount[l]>=n.blkIndent){var c=n.bMarks[l]+n.tShift[l];let e=n.eMarks[l];if(c<e&&(45===(u=n.src.charCodeAt(c))||61===u)&&(c=n.skipChars(c,u),n.skipSpaces(c)>=e)){a=61===u?1:2;break}}if(!(n.sCount[l]<0)){let r=!1;for(let e=0,t=s.length;e<t;e++)if(s[e](n,l,i,!0)){r=!0;break}if(r)break}}return!!a&&(t=n.getLines(e,l,n.blkIndent,!1).trim(),n.line=l+1,(r=n.push("heading_open","h"+String(a),1)).markup=String.fromCharCode(u),r.map=[e,n.line],(r=n.push("inline","",0)).content=t,r.map=[e,n.line-1],r.children=[],n.push("heading_close","h"+String(a),-1).markup=String.fromCharCode(u),n.parentType=o,!0)}],["paragraph",function(n,e,i){var s=n.md.block.ruler.getRules("paragraph"),t=n.parentType;let o=e+1;for(n.parentType="paragraph";o<i&&!n.isEmpty(o);o++)if(!(3<n.sCount[o]-n.blkIndent||n.sCount[o]<0)){let r=!1;for(let e=0,t=s.length;e<t;e++)if(s[e](n,o,i,!0)){r=!0;break}if(r)break}var r=n.getLines(e,o,n.blkIndent,!1).trim(),u=(n.line=o,n.push("paragraph_open","p",1).map=[e,n.line],n.push("inline","",0));return u.content=r,u.map=[e,n.line],u.children=[],n.push("paragraph_close","p",-1),n.parentType=t,!0}]];function b(){this.ruler=new t;for(let e=0;e<C.length;e++)this.ruler.push(C[e][0],C[e][1],{alt:(C[e][2]||[]).slice()})}function v(e,t,r,n){this.src=e,this.env=r,this.md=t,this.tokens=n,this.tokens_meta=Array(n.length),this.pos=0,this.posMax=this.src.length,this.level=0,this.pending="",this.pendingLevel=0,this.cache={},this.delimiters=[],this._prev_delimiters=[],this.backticks={},this.backticksScanned=!1,this.linkLevel=0}b.prototype.tokenize=function(n,e,i){var s=this.ruler.getRules(""),o=s.length,u=n.md.options.maxNesting;let a=e,l=!1;for(;a<i&&(n.line=a=n.skipEmptyLines(a),!(a>=i))&&!(n.sCount[a]<n.blkIndent);){if(n.level>=u){n.line=i;break}let t=n.line,r=!1;for(let e=0;e<o;e++)if(r=s[e](n,a,i,!1)){if(t>=n.line)throw new Error("block rule didn't increment state.line");break}if(!r)throw new Error("none of the block rules matched");n.tight=!l,n.isEmpty(n.line-1)&&(l=!0),(a=n.line)<i&&n.isEmpty(a)&&(l=!0,a++,n.line=a)}},b.prototype.parse=function(e,t,r,n){e&&(e=new this.State(e,t,r,n),this.tokenize(e,e.line,e.lineMax))},b.prototype.State=k,v.prototype.pushPending=function(){var e=new m("text","",0);return e.content=this.pending,e.level=this.pendingLevel,this.tokens.push(e),this.pending="",e},v.prototype.push=function(e,t,r){this.pending&&this.pushPending();e=new m(e,t,r);let n=null;return r<0&&(this.level--,this.delimiters=this._prev_delimiters.pop()),e.level=this.level,0<r&&(this.level++,this._prev_delimiters.push(this.delimiters),this.delimiters=[],n={delimiters:this.delimiters}),this.pendingLevel=this.level,this.tokens.push(e),this.tokens_meta.push(n),e},v.prototype.scanDelims=function(e,t){var r=this.posMax,n=this.src.charCodeAt(e),i=0<e?this.src.charCodeAt(e-1):32;let s=e;for(;s<r&&this.src.charCodeAt(s)===n;)s++;var e=s-e,o=s<r?this.src.charCodeAt(s):32,u=A(i)||E(String.fromCharCode(i)),a=A(o)||E(String.fromCharCode(o)),i=y(i),o=y(o),l=!o&&(!a||i||u),i=!i&&(!u||o||a);return{can_open:l&&(t||!i||u),can_close:i&&(t||!l||a),length:e}},v.prototype.Token=m;let Ve=/(?:^|[^a-z0-9.+-])([a-z][a-z0-9.+-]*)$/i,Ge=[];for(let e=0;e<256;e++)Ge.push(0);function We(r,n){let i,s=[],e=n.length;for(let t=0;t<e;t++){let e=n[t];var o;126===e.marker&&-1!==e.end&&(o=n[e.end],(i=r.tokens[e.token]).type="s_open",i.tag="s",i.nesting=1,i.markup="~~",i.content="",(i=r.tokens[o.token]).type="s_close",i.tag="s",i.nesting=-1,i.markup="~~",i.content="","text"===r.tokens[o.token-1].type)&&"~"===r.tokens[o.token-1].content&&s.push(o.token-1)}for(;s.length;){let e=s.pop(),t=e+1;for(;t<r.tokens.length&&"s_close"===r.tokens[t].type;)t++;e!==--t&&(i=r.tokens[t],r.tokens[t]=r.tokens[e],r.tokens[e]=i)}}"\\!\"#$%&'()*+,./:;<=>?@[]^_`{|}~-".split("").forEach(function(e){Ge[e.charCodeAt(0)]=1});var x={tokenize:function(t,e){var r=t.pos,n=t.src.charCodeAt(r);if(e)return!1;if(126!==n)return!1;var i=t.scanDelims(t.pos,!0);let s=i.length;var o=String.fromCharCode(n);if(s<2)return!1;let u;s%2&&((u=t.push("text","",0)).content=o,s--);for(let e=0;e<s;e+=2)(u=t.push("text","",0)).content=o+o,t.delimiters.push({marker:n,length:0,token:t.tokens.length-1,end:-1,open:i.can_open,close:i.can_close});return t.pos+=i.length,!0},postProcess:function(t){var r=t.tokens_meta,n=t.tokens_meta.length;We(t,t.delimiters);for(let e=0;e<n;e++)r[e]&&r[e].delimiters&&We(t,r[e].delimiters)}};function Je(t,r){for(let e=r.length-1;0<=e;e--){var n,i,s,o,u=r[e];95!==u.marker&&42!==u.marker||-1!==u.end&&(n=r[u.end],i=0<e&&r[e-1].end===u.end+1&&r[e-1].marker===u.marker&&r[e-1].token===u.token-1&&r[u.end+1].token===n.token+1,s=String.fromCharCode(u.marker),(o=t.tokens[u.token]).type=i?"strong_open":"em_open",o.tag=i?"strong":"em",o.nesting=1,o.markup=i?s+s:s,o.content="",(o=t.tokens[n.token]).type=i?"strong_close":"em_close",o.tag=i?"strong":"em",o.nesting=-1,o.markup=i?s+s:s,o.content="",i)&&(t.tokens[r[e-1].token].content="",t.tokens[r[u.end+1].token].content="",e--)}}let Qe={tokenize:function(t,e){var r=t.pos,n=t.src.charCodeAt(r);if(e)return!1;if(95!==n&&42!==n)return!1;var i=t.scanDelims(t.pos,42===n);for(let e=0;e<i.length;e++)t.push("text","",0).content=String.fromCharCode(n),t.delimiters.push({marker:n,length:i.length,token:t.tokens.length-1,end:-1,open:i.can_open,close:i.can_close});return t.pos+=i.length,!0},postProcess:function(t){var r=t.tokens_meta,n=t.tokens_meta.length;Je(t,t.delimiters);for(let e=0;e<n;e++)r[e]&&r[e].delimiters&&Je(t,r[e].delimiters)}},Xe=/^([a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*)$/,Ye=/^([a-zA-Z][a-zA-Z0-9+.-]{1,31}):([^<>\x00-\x20]*)$/,Ke=/^&#((?:x[a-f0-9]{1,6}|[0-9]{1,7}));/i,et=/^&([a-z][a-z0-9]{1,31});/i;function tt(u){let t={},r=u.length;if(r){let e=0,o=-2;var a=[];for(let s=0;s<r;s++){let i=u[s];if(a.push(0),u[e].marker===i.marker&&o===i.token-1||(e=s),o=i.token,i.length=i.length||0,i.close){t.hasOwnProperty(i.marker)||(t[i.marker]=[-1,-1,-1,-1,-1,-1]);var l=t[i.marker][(i.open?3:0)+i.length%3];let r=e-a[e]-1,n=r;for(;r>l;r-=a[r]+1){let t=u[r];if(t.marker===i.marker&&t.open&&t.end<0){let e=!1;if(!(e=!t.close&&!i.open||(t.length+i.length)%3!=0||t.length%3==0&&i.length%3==0?e:!0)){let e=0<r&&!u[r-1].open?a[r-1]+1:0;a[s]=s-r+e,a[r]=e,i.open=!1,t.end=s,t.close=!1,n=-1,o=-2;break}}}-1!==n&&(t[i.marker][(i.open?3:0)+(i.length||0)%3]=n)}}}}let rt=[["text",function(e,t){let r=e.pos;for(;r<e.posMax&&!function(e){switch(e){case 10:case 33:case 35:case 36:case 37:case 38:case 42:case 43:case 45:case 58:case 60:case 61:case 62:case 64:case 91:case 92:case 93:case 94:case 95:case 96:case 123:case 125:case 126:return 1;default:return}}(e.src.charCodeAt(r));)r++;return r!==e.pos&&(t||(e.pending+=e.src.slice(e.pos,r)),e.pos=r,!0)}],["linkify",function(r,e){if(!r.md.options.linkify)return!1;if(0<r.linkLevel)return!1;let t=r.pos;if(t+3>r.posMax)return!1;if(58!==r.src.charCodeAt(t))return!1;if(47!==r.src.charCodeAt(t+1))return!1;if(47!==r.src.charCodeAt(t+2))return!1;var n=r.pending.match(Ve);if(!n)return!1;var n=n[1],i=r.md.linkify.matchAtStart(r.src.slice(t-n.length));if(!i)return!1;let s=i.url;if(s.length<=n.length)return!1;s=s.replace(/\*+$/,"");i=r.md.normalizeLink(s);if(!r.md.validateLink(i))return!1;if(!e){r.pending=r.pending.slice(0,-n.length);let e=r.push("link_open","a",1),t=(e.attrs=[["href",i]],e.markup="linkify",e.info="auto",r.push("text","",0).content=r.md.normalizeLinkText(s),r.push("link_close","a",-1));t.markup="linkify",t.info="auto"}return r.pos+=s.length-n.length,!0}],["newline",function(t,e){let r=t.pos;if(10!==t.src.charCodeAt(r))return!1;var n=t.pending.length-1,i=t.posMax;if(!e)if(0<=n&&32===t.pending.charCodeAt(n))if(1<=n&&32===t.pending.charCodeAt(n-1)){let e=n-1;for(;1<=e&&32===t.pending.charCodeAt(e-1);)e--;t.pending=t.pending.slice(0,e),t.push("hardbreak","br",0)}else t.pending=t.pending.slice(0,-1),t.push("softbreak","br",0);else t.push("softbreak","br",0);for(r++;r<i&&g(t.src.charCodeAt(r));)r++;return t.pos=r,!0}],["escape",function(r,e){let n=r.pos;var i=r.posMax;if(92!==r.src.charCodeAt(n))return!1;if(++n>=i)return!1;let s=r.src.charCodeAt(n);if(10===s){for(e||r.push("hardbreak","br",0),n++;n<i&&g(s=r.src.charCodeAt(n));)n++;r.pos=n}else{let t=r.src[n];if(55296<=s&&s<=56319&&n+1<i){let e=r.src.charCodeAt(n+1);56320<=e&&e<=57343&&(t+=r.src[n+1],n++)}var o="\\"+t;if(!e){let e=r.push("text_special","",0);s<256&&0!==Ge[s]?e.content=t:e.content=o,e.markup=o,e.info="escape"}r.pos=n+1}return!0}],["backticks",function(n,i){let s=n.pos;if(96!==n.src.charCodeAt(s))return!1;let e=s;s++;for(var o=n.posMax;s<o&&96===n.src.charCodeAt(s);)s++;var u=n.src.slice(e,s),a=u.length;if(!(n.backticksScanned&&(n.backticks[a]||0)<=e)){let t,r=s;for(;-1!==(t=n.src.indexOf("`",r));){for(r=t+1;r<o&&96===n.src.charCodeAt(r);)r++;let e=r-t;if(e===a){if(!i){let e=n.push("code_inline","code",0);e.markup=u,e.content=n.src.slice(s,t).replace(/\n/g," ").replace(/^ (.+) $/,"$1")}return n.pos=r,!0}n.backticks[e]=t}n.backticksScanned=!0}return i||(n.pending+=u),n.pos+=a,!0}],["strikethrough",x.tokenize],["emphasis",Qe.tokenize],["link",function(t,e){let r,n,i,s,o="",u="",a=t.pos,l=!0;if(91!==t.src.charCodeAt(t.pos))return!1;var c=t.pos,h=t.posMax,p=t.pos+1,f=t.md.helpers.parseLinkLabel(t,t.pos,!0);if(f<0)return!1;let d=f+1;if(d<h&&40===t.src.charCodeAt(d)){for(l=!1,d++;d<h&&(g(r=t.src.charCodeAt(d))||10===r);d++);if(d>=h)return!1;if(a=d,(i=t.md.helpers.parseLinkDestination(t.src,d,t.posMax)).ok){for(o=t.md.normalizeLink(i.str),t.md.validateLink(o)?d=i.pos:o="",a=d;d<h&&(g(r=t.src.charCodeAt(d))||10===r);d++);if(i=t.md.helpers.parseLinkTitle(t.src,d,t.posMax),d<h&&a!==d&&i.ok)for(u=i.str,d=i.pos;d<h&&(g(r=t.src.charCodeAt(d))||10===r);d++);}(d>=h||41!==t.src.charCodeAt(d))&&(l=!0),d++}if(l){if(void 0===t.env.references)return!1;if(d<h&&91===t.src.charCodeAt(d)&&(a=d+1,0<=(d=t.md.helpers.parseLinkLabel(t,d)))?n=t.src.slice(a,d++):d=f+1,n=n||t.src.slice(p,f),!(s=t.env.references[_(n)]))return t.pos=c,!1;o=s.href,u=s.title}if(!e){t.pos=p,t.posMax=f;let e=[["href",o]];t.push("link_open","a",1).attrs=e,u&&e.push(["title",u]),t.linkLevel++,t.md.inline.tokenize(t),t.linkLevel--,t.push("link_close","a",-1)}return t.pos=d,t.posMax=h,!0}],["image",function(n,e){let t,i,r,s,o,u,a,l,c="";var h=n.pos,p=n.posMax;if(33!==n.src.charCodeAt(n.pos))return!1;if(91!==n.src.charCodeAt(n.pos+1))return!1;var f=n.pos+2,d=n.md.helpers.parseLinkLabel(n,n.pos+1,!1);if(d<0)return!1;if((s=d+1)<p&&40===n.src.charCodeAt(s)){for(s++;s<p&&(g(t=n.src.charCodeAt(s))||10===t);s++);if(s>=p)return!1;for(l=s,(u=n.md.helpers.parseLinkDestination(n.src,s,n.posMax)).ok&&(c=n.md.normalizeLink(u.str),n.md.validateLink(c)?s=u.pos:c=""),l=s;s<p&&(g(t=n.src.charCodeAt(s))||10===t);s++);if(u=n.md.helpers.parseLinkTitle(n.src,s,n.posMax),s<p&&l!==s&&u.ok)for(a=u.str,s=u.pos;s<p&&(g(t=n.src.charCodeAt(s))||10===t);s++);else a="";if(s>=p||41!==n.src.charCodeAt(s))return n.pos=h,!1;s++}else{if(void 0===n.env.references)return!1;if(s<p&&91===n.src.charCodeAt(s)&&(l=s+1,0<=(s=n.md.helpers.parseLinkLabel(n,s)))?r=n.src.slice(l,s++):s=d+1,r=r||n.src.slice(f,d),!(o=n.env.references[_(r)]))return n.pos=h,!1;c=o.href,a=o.title}if(!e){i=n.src.slice(f,d);let e=[],t=(n.md.inline.parse(i,n.md,n.env,e),n.push("image","img",0)),r=[["src",c],["alt",""]];t.attrs=r,t.children=e,t.content=i,a&&r.push(["title",a])}return n.pos=s,n.posMax=p,!0}],["autolink",function(n,i){let r=n.pos;if(60===n.src.charCodeAt(r)){let e=n.pos,t=n.posMax;for(;;){if(++r>=t)return!1;let e=n.src.charCodeAt(r);if(60===e)return!1;if(62===e)break}var s=n.src.slice(e+1,r);if(Ye.test(s)){let r=n.md.normalizeLink(s);if(!n.md.validateLink(r))return!1;if(!i){let e=n.push("link_open","a",1),t=(e.attrs=[["href",r]],e.markup="autolink",e.info="auto",n.push("text","",0).content=n.md.normalizeLinkText(s),n.push("link_close","a",-1));t.markup="autolink",t.info="auto"}return n.pos+=s.length+2,!0}if(Xe.test(s)){let r=n.md.normalizeLink("mailto:"+s);if(!n.md.validateLink(r))return!1;if(!i){let e=n.push("link_open","a",1),t=(e.attrs=[["href",r]],e.markup="autolink",e.info="auto",n.push("text","",0).content=n.md.normalizeLinkText(s),n.push("link_close","a",-1));t.markup="autolink",t.info="auto"}return n.pos+=s.length+2,!0}}return!1}],["html_inline",function(t,e){if(!t.md.options.html)return!1;var r=t.posMax,n=t.pos;if(60!==t.src.charCodeAt(n)||r<=n+2)return!1;var r=t.src.charCodeAt(n+1);if(33!==r&&63!==r&&47!==r&&!(97<=(r=32|r)&&r<=122))return!1;r=t.src.slice(n).match(Ue);if(!r)return!1;if(!e){let e=t.push("html_inline","",0);e.content=r[0],n=e.content,/^<a[>\s]/i.test(n)&&t.linkLevel++,/^<\/a\s*>/i.test(e.content)&&t.linkLevel--}return t.pos+=r[0].length,!0}],["entity",function(n,e){let t=n.pos,r=n.posMax;if(38===n.src.charCodeAt(t)&&!(t+1>=r))if(35===n.src.charCodeAt(t+1)){let r=n.src.slice(t).match(Ke);if(r){if(!e){let e="x"===r[1][0].toLowerCase()?parseInt(r[1].slice(1),16):parseInt(r[1],10),t=n.push("text_special","",0);t.content=_e(e)?h(e):h(65533),t.markup=r[0],t.info="entity"}return n.pos+=r[0].length,!0}}else{let r=n.src.slice(t).match(et);if(r){let t=he(r[0]);if(t!==r[0]){if(!e){let e=n.push("text_special","",0);e.content=t,e.markup=r[0],e.info="entity"}return n.pos+=r[0].length,!0}}}return!1}]],nt=[["balance_pairs",function(e){var t=e.tokens_meta,r=e.tokens_meta.length;tt(e.delimiters);for(let e=0;e<r;e++)t[e]&&t[e].delimiters&&tt(t[e].delimiters)}],["strikethrough",x.postProcess],["emphasis",Qe.postProcess],["fragments_join",function(e){let t,r,n=0;var i=e.tokens,s=e.tokens.length;for(t=r=0;t<s;t++)i[t].nesting<0&&n--,i[t].level=n,0<i[t].nesting&&n++,"text"===i[t].type&&t+1<s&&"text"===i[t+1].type?i[t+1].content=i[t].content+i[t+1].content:(t!==r&&(i[r]=i[t]),r++);t!==r&&(i.length=r)}]];function w(){this.ruler=new t;for(let e=0;e<rt.length;e++)this.ruler.push(rt[e][0],rt[e][1]);this.ruler2=new t;for(let e=0;e<nt.length;e++)this.ruler2.push(nt[e][0],nt[e][1])}function it(r){return Array.prototype.slice.call(arguments,1).forEach(function(t){t&&Object.keys(t).forEach(function(e){r[e]=t[e]})}),r}function z(e){return Object.prototype.toString.call(e)}function st(e){return"[object Function]"===z(e)}function ot(e){return e.replace(/[.?*+^$[\]\\(){}|-]/g,"\\$&")}w.prototype.skipToken=function(r){var n=r.pos,i=this.ruler.getRules(""),s=i.length,e=r.md.options.maxNesting,o=r.cache;if(void 0!==o[n])r.pos=o[n];else{let t=!1;if(r.level<e){for(let e=0;e<s;e++)if(r.level++,t=i[e](r,!0),r.level--,t){if(r.pos<=n)throw new Error("inline rule didn't increment state.pos");break}}else r.pos=r.posMax;t||r.pos++,o[n]=r.pos}},w.prototype.tokenize=function(r){for(var n=this.ruler.getRules(""),i=n.length,e=r.posMax,s=r.md.options.maxNesting;r.pos<e;){var o=r.pos;let t=!1;if(r.level<s)for(let e=0;e<i;e++)if(t=n[e](r,!1)){if(o>=r.pos)throw new Error("inline rule didn't increment state.pos");break}if(t){if(e<=r.pos)break}else r.pending+=r.src[r.pos++]}r.pending&&r.pushPending()},w.prototype.parse=function(e,t,r,n){var i=new this.State(e,t,r,n),s=(this.tokenize(i),this.ruler2.getRules("")),o=s.length;for(let e=0;e<o;e++)s[e](i)},w.prototype.State=v;let ut={fuzzyLink:!0,fuzzyEmail:!0,fuzzyIP:!1},at={"http:":{validate:function(e,t,r){e=e.slice(t);return r.re.http||(r.re.http=new RegExp("^\\/\\/"+r.re.src_auth+r.re.src_host_port_strict+r.re.src_path,"i")),r.re.http.test(e)?e.match(r.re.http)[0].length:0}},"https:":"http:","ftp:":"http:","//":{validate:function(e,t,r){var n=e.slice(t);return r.re.no_http||(r.re.no_http=new RegExp("^"+r.re.src_auth+"(?:localhost|(?:(?:"+r.re.src_domain+")\\.)+"+r.re.src_domain_root+")"+r.re.src_port+r.re.src_host_terminator+r.re.src_path,"i")),!r.re.no_http.test(n)||3<=t&&":"===e[t-3]||3<=t&&"/"===e[t-3]?0:n.match(r.re.no_http)[0].length}},"mailto:":{validate:function(e,t,r){e=e.slice(t);return r.re.mailto||(r.re.mailto=new RegExp("^"+r.re.src_email_name+"@"+r.re.src_host_strict,"i")),r.re.mailto.test(e)?e.match(r.re.mailto)[0].length:0}}},lt="a[cdefgilmnoqrstuwxz]|b[abdefghijmnorstvwyz]|c[acdfghiklmnoruvwxyz]|d[ejkmoz]|e[cegrstu]|f[ijkmor]|g[abdefghilmnpqrstuwy]|h[kmnrtu]|i[delmnoqrst]|j[emop]|k[eghimnprwyz]|l[abcikrstuvy]|m[acdeghklmnopqrstuvwxyz]|n[acefgilopruz]|om|p[aefghklmnrstwy]|qa|r[eosuw]|s[abcdeghijklmnortuvxyz]|t[cdfghjklmnortvwz]|u[agksyz]|v[aceginu]|w[fs]|y[et]|z[amw]",ct="biz|com|edu|gov|net|org|pro|web|xxx|aero|asia|coop|info|museum|name|shop|рф".split("|");function ht(i){let t=i.re=function(e){var t={};e=i.__opts__||{},t.src_Any=X.source,t.src_Cc=Y.source,t.src_Z=te.source,t.src_P=K.source,t.src_ZPCc=[t.src_Z,t.src_P,t.src_Cc].join("|"),t.src_ZCc=[t.src_Z,t.src_Cc].join("|");return t.src_pseudo_letter="(?:(?![><｜]|"+t.src_ZPCc+")"+t.src_Any+")",t.src_ip4="(?:(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)",t.src_auth="(?:(?:(?!"+t.src_ZCc+"|[@/\\[\\]()]).)+@)?",t.src_port="(?::(?:6(?:[0-4]\\d{3}|5(?:[0-4]\\d{2}|5(?:[0-2]\\d|3[0-5])))|[1-5]?\\d{1,4}))?",t.src_host_terminator="(?=$|[><｜]|"+t.src_ZPCc+")(?!"+(e["---"]?"-(?!--)|":"-|")+"_|:\\d|\\.-|\\.(?!$|"+t.src_ZPCc+"))",t.src_path="(?:[/?#](?:(?!"+t.src_ZCc+"|[><｜]|[()[\\]{}.,\"'?!\\-;]).|\\[(?:(?!"+t.src_ZCc+"|\\]).)*\\]|\\((?:(?!"+t.src_ZCc+"|[)]).)*\\)|\\{(?:(?!"+t.src_ZCc+'|[}]).)*\\}|\\"(?:(?!'+t.src_ZCc+'|["]).)+\\"|\\\'(?:(?!'+t.src_ZCc+"|[']).)+\\'|\\'(?="+t.src_pseudo_letter+"|[-])|\\.{2,}[a-zA-Z0-9%/&]|\\.(?!"+t.src_ZCc+"|[.]|$)|"+(e["---"]?"\\-(?!--(?:[^-]|$))(?:-*)|":"\\-+|")+",(?!"+t.src_ZCc+"|$)|;(?!"+t.src_ZCc+"|$)|\\!+(?!"+t.src_ZCc+"|[!]|$)|\\?(?!"+t.src_ZCc+"|[?]|$))+|\\/)?",t.src_email_name='[\\-;:&=\\+\\$,\\.a-zA-Z0-9_][\\-;:&=\\+\\$,\\"\\.a-zA-Z0-9_]*',t.src_xn="xn--[a-z0-9\\-]{1,59}",t.src_domain_root="(?:"+t.src_xn+"|"+t.src_pseudo_letter+"{1,63})",t.src_domain="(?:"+t.src_xn+"|(?:"+t.src_pseudo_letter+")|(?:"+t.src_pseudo_letter+"(?:-|"+t.src_pseudo_letter+"){0,61}"+t.src_pseudo_letter+"))",t.src_host="(?:(?:(?:(?:"+t.src_domain+")\\.)*"+t.src_domain+"))",t.tpl_host_fuzzy="(?:"+t.src_ip4+"|(?:(?:(?:"+t.src_domain+")\\.)+(?:%TLDS%)))",t.tpl_host_no_ip_fuzzy="(?:(?:(?:"+t.src_domain+")\\.)+(?:%TLDS%))",t.src_host_strict=t.src_host+t.src_host_terminator,t.tpl_host_fuzzy_strict=t.tpl_host_fuzzy+t.src_host_terminator,t.src_host_port_strict=t.src_host+t.src_port+t.src_host_terminator,t.tpl_host_port_fuzzy_strict=t.tpl_host_fuzzy+t.src_port+t.src_host_terminator,t.tpl_host_port_no_ip_fuzzy_strict=t.tpl_host_no_ip_fuzzy+t.src_port+t.src_host_terminator,t.tpl_host_fuzzy_test="localhost|www\\.|\\.\\d{1,3}\\.|(?:\\.(?:%TLDS%)(?:"+t.src_ZPCc+"|>|$))",t.tpl_email_fuzzy='(^|[><｜]|"|\\(|'+t.src_ZCc+")("+t.src_email_name+"@"+t.tpl_host_fuzzy_strict+")",t.tpl_link_fuzzy="(^|(?![.:/\\-_@])(?:[$+<=>^`|｜]|"+t.src_ZPCc+"))((?![$+<=>^`|｜])"+t.tpl_host_port_fuzzy_strict+t.src_path+")",t.tpl_link_no_ip_fuzzy="(^|(?![.:/\\-_@])(?:[$+<=>^`|｜]|"+t.src_ZPCc+"))((?![$+<=>^`|｜])"+t.tpl_host_port_no_ip_fuzzy_strict+t.src_path+")",t}(),e=i.__tlds__.slice();function r(e){return e.replace("%TLDS%",t.src_tlds)}i.onCompile(),i.__tlds_replaced__||e.push(lt),e.push(t.src_xn),t.src_tlds=e.join("|"),t.email_fuzzy=RegExp(r(t.tpl_email_fuzzy),"i"),t.link_fuzzy=RegExp(r(t.tpl_link_fuzzy),"i"),t.link_no_ip_fuzzy=RegExp(r(t.tpl_link_no_ip_fuzzy),"i"),t.host_fuzzy_test=RegExp(r(t.tpl_host_fuzzy_test),"i");let s=[];function o(e,t){throw new Error('(LinkifyIt) Invalid schema "'+e+'": '+t)}i.__compiled__={},Object.keys(i.__schemas__).forEach(function(e){var t,r,n=i.__schemas__[e];null!==n&&(i.__compiled__[e]=t={validate:null,link:null},"[object Object]"===z(n)?("[object RegExp]"!==z(n.validate)?st(n.validate)?t.validate=n.validate:o(e,n):t.validate=(r=n.validate,function(e,t){e=e.slice(t);return r.test(e)?e.match(r)[0].length:0}),st(n.normalize)?t.normalize=n.normalize:n.normalize?o(e,n):t.normalize=function(e,t){t.normalize(e)}):"[object String]"!==z(n)?o(e,n):s.push(e))}),s.forEach(function(e){i.__compiled__[i.__schemas__[e]]&&(i.__compiled__[e].validate=i.__compiled__[i.__schemas__[e]].validate,i.__compiled__[e].normalize=i.__compiled__[i.__schemas__[e]].normalize)}),i.__compiled__[""]={validate:null,normalize:function(e,t){t.normalize(e)}};var n=Object.keys(i.__compiled__).filter(function(e){return 0<e.length&&i.__compiled__[e]}).map(ot).join("|");i.re.schema_test=RegExp("(^|(?!_)(?:[><｜]|"+t.src_ZPCc+"))("+n+")","i"),i.re.schema_search=RegExp("(^|(?!_)(?:[><｜]|"+t.src_ZPCc+"))("+n+")","ig"),i.re.schema_at_start=RegExp("^"+i.re.schema_search.source,"i"),i.re.pretest=RegExp("("+i.re.schema_test.source+")|("+i.re.host_fuzzy_test.source+")|@","i"),(n=i).__index__=-1,n.__text_cache__=""}function pt(e,t){var r=e.__index__,n=e.__last_index__,i=e.__text_cache__.slice(r,n);this.schema=e.__schema__.toLowerCase(),this.index=r+t,this.lastIndex=n+t,this.raw=i,this.text=i,this.url=i}function ft(e,t){t=new pt(e,t);return e.__compiled__[t.schema].normalize(t,e),t}function S(e,t){if(!(this instanceof S))return new S(e,t);var r;t||(r=e,Object.keys(r||{}).reduce(function(e,t){return e||ut.hasOwnProperty(t)},!1)&&(t=e,e={})),this.__opts__=it({},ut,t),this.__index__=-1,this.__last_index__=-1,this.__schema__="",this.__text_cache__="",this.__schemas__=it({},at,e),this.__compiled__={},this.__tlds__=ct,this.__tlds_replaced__=!1,this.re={},ht(this)}S.prototype.add=function(e,t){return this.__schemas__[e]=t,ht(this),this},S.prototype.set=function(e){return this.__opts__=it(this.__opts__,e),this},S.prototype.test=function(e){if(this.__text_cache__=e,this.__index__=-1,!e.length)return!1;let t,r,n,i,s,o,u,a;if(this.re.schema_test.test(e))for((u=this.re.schema_search).lastIndex=0;null!==(t=u.exec(e));)if(i=this.testSchemaAt(e,t[2],u.lastIndex)){this.__schema__=t[2],this.__index__=t.index+t[1].length,this.__last_index__=t.index+t[0].length+i;break}return this.__opts__.fuzzyLink&&this.__compiled__["http:"]&&0<=(a=e.search(this.re.host_fuzzy_test))&&(this.__index__<0||a<this.__index__)&&null!==(r=e.match(this.__opts__.fuzzyIP?this.re.link_fuzzy:this.re.link_no_ip_fuzzy))&&(s=r.index+r[1].length,this.__index__<0||s<this.__index__)&&(this.__schema__="",this.__index__=s,this.__last_index__=r.index+r[0].length),this.__opts__.fuzzyEmail&&this.__compiled__["mailto:"]&&0<=e.indexOf("@")&&null!==(n=e.match(this.re.email_fuzzy))&&(s=n.index+n[1].length,o=n.index+n[0].length,this.__index__<0||s<this.__index__||s===this.__index__&&o>this.__last_index__)&&(this.__schema__="mailto:",this.__index__=s,this.__last_index__=o),0<=this.__index__},S.prototype.pretest=function(e){return this.re.pretest.test(e)},S.prototype.testSchemaAt=function(e,t,r){return this.__compiled__[t.toLowerCase()]?this.__compiled__[t.toLowerCase()].validate(e,r,this):0},S.prototype.match=function(e){var t=[];let r=0,n=(0<=this.__index__&&this.__text_cache__===e&&(t.push(ft(this,r)),r=this.__last_index__),r?e.slice(r):e);for(;this.test(n);)t.push(ft(this,r)),n=n.slice(this.__last_index__),r+=this.__last_index__;return t.length?t:null},S.prototype.matchAtStart=function(e){var t;return this.__text_cache__=e,this.__index__=-1,e.length&&(t=this.re.schema_at_start.exec(e))&&(e=this.testSchemaAt(e,t[2],t[0].length))?(this.__schema__=t[2],this.__index__=t.index+t[1].length,this.__last_index__=t.index+t[0].length+e,ft(this,0)):null},S.prototype.tlds=function(e,t){return e=Array.isArray(e)?e:[e],t?this.__tlds__=this.__tlds__.concat(e).sort().filter(function(e,t,r){return e!==r[t-1]}).reverse():(this.__tlds__=e.slice(),this.__tlds_replaced__=!0),ht(this),this},S.prototype.normalize=function(e){e.schema||(e.url="http://"+e.url),"mailto:"!==e.schema||/^mailto:/i.test(e.url)||(e.url="mailto:"+e.url)},S.prototype.onCompile=function(){};let q=2147483647,B=36,dt=/^xn--/,_t=/[^\0-\x7F]/,mt=/[\x2E\u3002\uFF0E\uFF61]/g,gt={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},L=Math.floor,kt=String.fromCharCode;function I(e){throw new RangeError(gt[e])}function Dt(e,t){var r=e.split("@");let n="";1<r.length&&(n=r[0]+"@",e=r[1]);r=function(e,t){var r=[];let n=e.length;for(;n--;)r[n]=t(e[n]);return r}((e=e.replace(mt,".")).split("."),t).join(".");return n+r}function Ct(t){var r=[];let n=0,e=t.length;for(;n<e;){var i=t.charCodeAt(n++);if(55296<=i&&i<=56319&&n<e){let e=t.charCodeAt(n++);56320==(64512&e)?r.push(((1023&i)<<10)+(1023&e)+65536):(r.push(i),n--)}else r.push(i)}return r}function yt(e,t){return e+22+75*(e<26)-((0!=t)<<5)}function Et(e,t,r){let n=0;for(e=r?L(e/700):e>>1,e+=L(e/t);455<e;n+=B)e=L(e/35);return L(n+36*e/(e+38))}function At(i){var t=[],s=i.length;let o=0,r=128,u=72,a=i.lastIndexOf("-");a<0&&(a=0);for(let e=0;e<a;++e)128<=i.charCodeAt(e)&&I("not-basic"),t.push(i.charCodeAt(e));for(let n=0<a?a+1:0;n<s;){let e=o;for(let t=1,r=B;;r+=B){n>=s&&I("invalid-input");let e=48<=(l=i.charCodeAt(n++))&&l<58?l-48+26:65<=l&&l<91?l-65:97<=l&&l<123?l-97:B;e>=B&&I("invalid-input"),e>L((q-o)/t)&&I("overflow"),o+=e*t;var l=r<=u?1:r>=u+26?26:r-u;if(e<l)break;l=B-l;t>L(q/l)&&I("overflow"),t*=l}var c=t.length+1;u=Et(o-e,c,0==e),L(o/c)>q-r&&I("overflow"),r+=L(o/c),o%=c,t.splice(o++,0,r)}return String.fromCodePoint(...t)}function Ft(r){let s=[],e=(r=Ct(r)).length,n=128,o=0,u=72;for(let e of r)e<128&&s.push(kt(e));let a=s.length,l=a;for(a&&s.push("-");l<e;){let t=q;for(let e of r)e>=n&&e<t&&(t=e);var c=l+1;t-n>L((q-o)/c)&&I("overflow"),o+=(t-n)*c,n=t;for(let e of r)if(e<n&&++o>q&&I("overflow"),e===n){let i=o;for(let n=B;;n+=B){let e=n<=u?1:n>=u+26?26:n-u;if(i<e)break;let t=i-e,r=B-e;s.push(kt(yt(e+t%r,0))),i=L(t/r)}s.push(kt(yt(i,0))),u=Et(o,c,l===a),o=0,++l}++o,++n}return s.join("")}let bt={version:"2.3.1",ucs2:{decode:Ct,encode:e=>String.fromCodePoint(...e)},decode:At,encode:Ft,toASCII:function(e){return Dt(e,function(e){return _t.test(e)?"xn--"+Ft(e):e})},toUnicode:function(e){return Dt(e,function(e){return dt.test(e)?At(e.slice(4).toLowerCase()):e})}},vt={default:{options:{html:!1,xhtmlOut:!1,breaks:!1,langPrefix:"language-",linkify:!1,typographer:!1,quotes:"“”‘’",highlight:null,maxNesting:100},components:{core:{},block:{},inline:{}}},zero:{options:{html:!1,xhtmlOut:!1,breaks:!1,langPrefix:"language-",linkify:!1,typographer:!1,quotes:"“”‘’",highlight:null,maxNesting:20},components:{core:{rules:["normalize","block","inline","text_join"]},block:{rules:["paragraph"]},inline:{rules:["text"],rules2:["balance_pairs","fragments_join"]}}},commonmark:{options:{html:!0,xhtmlOut:!0,breaks:!1,langPrefix:"language-",linkify:!1,typographer:!1,quotes:"“”‘’",highlight:null,maxNesting:20},components:{core:{rules:["normalize","block","inline","text_join"]},block:{rules:["blockquote","code","fence","heading","hr","html_block","lheading","list","reference","paragraph"]},inline:{rules:["autolink","backticks","emphasis","entity","escape","html_inline","image","link","newline","text"],rules2:["balance_pairs","emphasis","fragments_join"]}}}},xt=/^(vbscript|javascript|file|data):/,wt=/^data:image\/(gif|png|jpeg|webp);/;function zt(e){e=e.trim().toLowerCase();return!xt.test(e)||wt.test(e)}let St=["http:","https:","mailto:"];function qt(e){var t=J(e,!0);if(t.hostname&&(!t.protocol||0<=St.indexOf(t.protocol)))try{t.hostname=bt.toASCII(t.hostname)}catch(e){}return r(i(t))}function Bt(e){var t=J(e,!0);if(t.hostname&&(!t.protocol||0<=St.indexOf(t.protocol)))try{t.hostname=bt.toUnicode(t.hostname)}catch(e){}return n(i(t),n.defaultChars+"%")}function M(e,t){if(!(this instanceof M))return new M(e,t);t||pe(e)||(t=e||{},e="default"),this.inline=new w,this.block=new b,this.core=new Re,this.renderer=new d,this.linkify=new S,this.validateLink=zt,this.normalizeLink=qt,this.normalizeLinkText=Bt,this.utils=Fe,this.helpers=c({},be),this.options={},this.configure(e),t&&this.set(t)}return M.prototype.set=function(e){return c(this.options,e),this},M.prototype.configure=function(t){let r=this;if(pe(t)){let e=t;if(!(t=vt[e]))throw new Error('Wrong `markdown-it` preset "'+e+'", check name')}if(t)return t.options&&r.set(t.options),t.components&&Object.keys(t.components).forEach(function(e){t.components[e].rules&&r[e].ruler.enableOnly(t.components[e].rules),t.components[e].rules2&&r[e].ruler2.enableOnly(t.components[e].rules2)}),this;throw new Error("Wrong `markdown-it` preset, can't be empty")},M.prototype.enable=function(t,e){let r=[];Array.isArray(t)||(t=[t]),["core","block","inline"].forEach(function(e){r=r.concat(this[e].ruler.enable(t,!0))},this),r=r.concat(this.inline.ruler2.enable(t,!0));var n=t.filter(function(e){return r.indexOf(e)<0});if(n.length&&!e)throw new Error("MarkdownIt. Failed to enable unknown rule(s): "+n);return this},M.prototype.disable=function(t,e){let r=[];Array.isArray(t)||(t=[t]),["core","block","inline"].forEach(function(e){r=r.concat(this[e].ruler.disable(t,!0))},this),r=r.concat(this.inline.ruler2.disable(t,!0));var n=t.filter(function(e){return r.indexOf(e)<0});if(n.length&&!e)throw new Error("MarkdownIt. Failed to disable unknown rule(s): "+n);return this},M.prototype.use=function(e){var t=[this].concat(Array.prototype.slice.call(arguments,1));return e.apply(e,t),this},M.prototype.parse=function(e,t){if("string"!=typeof e)throw new Error("Input data should be a String");e=new this.core.State(e,this,t);return this.core.process(e),e.tokens},M.prototype.render=function(e,t){return this.renderer.render(this.parse(e,t=t||{}),this.options,t)},M.prototype.parseInline=function(e,t){e=new this.core.State(e,this,t);return e.inlineMode=!0,this.core.process(e),e.tokens},M.prototype.renderInline=function(e,t){return this.renderer.render(this.parseInline(e,t=t||{}),this.options,t)},M});