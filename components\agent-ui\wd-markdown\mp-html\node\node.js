"use strict";function t(e){"@babel/helpers - typeof";return(t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(e)}function e(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,i)}return r}function r(t){for(var r=1;r<arguments.length;r++){var o=null!=arguments[r]?arguments[r]:{};r%2?e(Object(o),!0).forEach(function(e){i(t,e,o[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(o)):e(Object(o)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(o,e))})}return t}function i(t,e,r){return(e=o(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function o(e){var r=s(e,"string");return"symbol"==t(r)?r:r+""}function s(e,r){if("object"!=t(e)||!e)return e;var i=e[Symbol.toPrimitive];if(void 0!==i){var o=i.call(e,r||"default");if("object"!=t(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}Component({data:{ctrl:{},isiOS:wx.getSystemInfoSync().system.includes("iOS")},properties:{childs:Array,opts:Array},options:{addGlobalClass:!0},attached:function(){this.triggerEvent("add",this,{bubbles:!0,composed:!0})},methods:{noop:function(){},getNode:function(t){try{for(var e=t.split("_"),r=this.data.childs[e[0]],i=1;i<e.length;i++)r=r.children[e[i]];return r}catch(t){return{text:"",attrs:{},children:[]}}},play:function(t){var e=t.target.dataset.i,i=this.getNode(e);if(this.root.triggerEvent("play",{source:i.name,attrs:r(r({},i.attrs),{},{src:i.src[this.data.ctrl[e]||0]})}),this.root.data.pauseVideo){for(var o=!1,s=t.target.id,n=this.root._videos.length;n--;)this.root._videos[n].id===s?o=!0:this.root._videos[n].pause();if(!o){var a=wx.createVideoContext(s,this);a.id=s,this.root.playbackRate&&a.playbackRate(this.root.playbackRate),this.root._videos.push(a)}}},imgTap:function(t){var e=this.getNode(t.target.dataset.i);if(e.a)return this.linkTap(e.a);if(!e.attrs.ignore&&(this.root.triggerEvent("imgtap",e.attrs),this.root.data.previewImg)){var r=this.root.imgList[e.i];wx.previewImage({showmenu:this.root.data.showImgMenu,current:r,urls:this.root.imgList})}},imgLoad:function(t){var e,r=t.target.dataset.i,o=this.getNode(r);o.w?(this.data.opts[1]&&!this.data.ctrl[r]||-1===this.data.ctrl[r])&&(e=1):e=t.detail.width,e&&this.setData(i({},"ctrl."+r,e)),this.checkReady()},checkReady:function(){var t=this;this.root.data.lazyLoad||(this.root.imgList._unloadimgs-=1,this.root.imgList._unloadimgs||setTimeout(function(){t.root.getRect().then(function(e){t.root.triggerEvent("ready",e)}).catch(function(){t.root.triggerEvent("ready",{})})},350))},linkTap:function(t){var e=t.currentTarget?this.getNode(t.currentTarget.dataset.i):{},r=e.attrs||t,i=r.href;this.root.triggerEvent("linktap",Object.assign({innerText:this.root.getText(e.children||[])},r)),i&&("#"===i[0]?this.root.navigateTo(i.substring(1)).catch(function(){}):i.split("?")[0].includes("://")?this.root.data.copyLink&&wx.setClipboardData({data:i,success:function(){return wx.showToast({title:"链接已复制"})}}):wx.navigateTo({url:i,fail:function(){wx.switchTab({url:i,fail:function(){}})}}))},mediaError:function(t){var e=t.target.dataset.i,r=this.getNode(e);if("video"===r.name||"audio"===r.name){var o=(this.data.ctrl[e]||0)+1;if(o>r.src.length&&(o=0),o<r.src.length)return this.setData(i({},"ctrl."+e,o))}else"img"===r.name&&(this.data.opts[2]&&this.setData(i({},"ctrl."+e,-1)),this.checkReady());this.root&&this.root.triggerEvent("error",{source:r.name,attrs:r.attrs,errMsg:t.detail.errMsg})}}});