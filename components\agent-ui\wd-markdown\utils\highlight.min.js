function deepFreeze(t){return t instanceof Map?t.clear=t.delete=t.set=function(){throw new Error("map is read-only")}:t instanceof Set&&(t.add=t.clear=t.delete=function(){throw new Error("set is read-only")}),Object.freeze(t),Object.getOwnPropertyNames(t).forEach(e=>{var e=t[e],n=typeof e;"object"!=n&&"function"!=n||Object.isFrozen(e)||deepFreeze(e)}),t}class Response{constructor(e){void 0===e.data&&(e.data={}),this.data=e.data,this.isMatchIgnored=!1}ignoreMatch(){this.isMatchIgnored=!0}}function escapeHTML(e){return e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;")}function inherit$1(e,...n){let t=Object.create(null);for(var i in e)t[i]=e[i];return n.forEach(function(e){for(var n in e)t[n]=e[n]}),t}let SPAN_CLOSE="</span>",emitsWrappingTags=e=>!!e.scope,scopeToCSSClass=(e,{prefix:n})=>{var t;return e.startsWith("language:")?e.replace("language:","language-"):e.includes(".")?[""+n+(t=e.split(".")).shift(),...t.map((e,n)=>""+e+"_".repeat(n+1))].join(" "):""+n+e};class HTMLRenderer{constructor(e,n){this.buffer="",this.classPrefix=n.classPrefix,e.walk(this)}addText(e){this.buffer+=escapeHTML(e)}openNode(e){emitsWrappingTags(e)&&(e=scopeToCSSClass(e.scope,{prefix:this.classPrefix}),this.span(e))}closeNode(e){emitsWrappingTags(e)&&(this.buffer+=SPAN_CLOSE)}value(){return this.buffer}span(e){this.buffer+=`<span class="${e}">`}}let newNode=(e={})=>{var n={children:[]};return Object.assign(n,e),n};class TokenTree{constructor(){this.rootNode=newNode(),this.stack=[this.rootNode]}get top(){return this.stack[this.stack.length-1]}get root(){return this.rootNode}add(e){this.top.children.push(e)}openNode(e){e=newNode({scope:e});this.add(e),this.stack.push(e)}closeNode(){if(1<this.stack.length)return this.stack.pop()}closeAllNodes(){for(;this.closeNode(););}toJSON(){return JSON.stringify(this.rootNode,null,4)}walk(e){return this.constructor._walk(e,this.rootNode)}static _walk(n,e){return"string"==typeof e?n.addText(e):e.children&&(n.openNode(e),e.children.forEach(e=>this._walk(n,e)),n.closeNode(e)),n}static _collapse(e){"string"!=typeof e&&e.children&&(e.children.every(e=>"string"==typeof e)?e.children=[e.children.join("")]:e.children.forEach(e=>{TokenTree._collapse(e)}))}}class TokenTreeEmitter extends TokenTree{constructor(e){super(),this.options=e}addText(e){""!==e&&this.add(e)}startScope(e){this.openNode(e)}endScope(){this.closeNode()}__addSublanguage(e,n){e=e.root;n&&(e.scope="language:"+n),this.add(e)}toHTML(){return new HTMLRenderer(this,this.options).value()}finalize(){return this.closeAllNodes(),!0}}function source(e){return e?"string"==typeof e?e:e.source:null}function lookahead(e){return concat("(?=",e,")")}function anyNumberOfTimes(e){return concat("(?:",e,")*")}function optional(e){return concat("(?:",e,")?")}function concat(...e){return e.map(e=>source(e)).join("")}function stripOptionsFromArgs(e){var n=e[e.length-1];return"object"==typeof n&&n.constructor===Object?(e.splice(e.length-1,1),n):{}}function either(...e){return"("+(stripOptionsFromArgs(e).capture?"":"?:")+e.map(e=>source(e)).join("|")+")"}function countMatchGroups(e){return new RegExp(e.toString()+"|").exec("").length-1}function startsWith(e,n){e=e&&e.exec(n);return e&&0===e.index}let BACKREF_RE=/\[(?:[^\\\]]|\\.)*\]|\(\??|\\([1-9][0-9]*)|\\./;function _rewriteBackreferences(e,{joinWith:n}){let o=0;return e.map(e=>{var n=o+=1;let t=source(e),i="";for(;0<t.length;){var r=BACKREF_RE.exec(t);if(!r){i+=t;break}i+=t.substring(0,r.index),t=t.substring(r.index+r[0].length),"\\"===r[0][0]&&r[1]?i+="\\"+String(Number(r[1])+n):(i+=r[0],"("===r[0]&&o++)}return i}).map(e=>`(${e})`).join(n)}let MATCH_NOTHING_RE=/\b\B/,IDENT_RE="[a-zA-Z]\\w*",UNDERSCORE_IDENT_RE="[a-zA-Z_]\\w*",NUMBER_RE="\\b\\d+(\\.\\d+)?",C_NUMBER_RE="(-?)(\\b0[xX][a-fA-F0-9]+|(\\b\\d+(\\.\\d*)?|\\.\\d+)([eE][-+]?\\d+)?)",BINARY_NUMBER_RE="\\b(0b[01]+)",RE_STARTERS_RE="!|!=|!==|%|%=|&|&&|&=|\\*|\\*=|\\+|\\+=|,|-|-=|/=|/|:|;|<<|<<=|<=|<|===|==|=|>>>=|>>=|>=|>>>|>>|>|\\?|\\[|\\{|\\(|\\^|\\^=|\\||\\|=|\\|\\||~",SHEBANG=(e={})=>{var n=/^#![ ]*\//;return e.binary&&(e.begin=concat(n,/.*\b/,e.binary,/\b.*/)),inherit$1({scope:"meta",begin:n,end:/$/,relevance:0,"on:begin":(e,n)=>{0!==e.index&&n.ignoreMatch()}},e)},BACKSLASH_ESCAPE={begin:"\\\\[\\s\\S]",relevance:0},APOS_STRING_MODE={scope:"string",begin:"'",end:"'",illegal:"\\n",contains:[BACKSLASH_ESCAPE]},QUOTE_STRING_MODE={scope:"string",begin:'"',end:'"',illegal:"\\n",contains:[BACKSLASH_ESCAPE]},PHRASAL_WORDS_MODE={begin:/\b(a|an|the|are|I'm|isn't|don't|doesn't|won't|but|just|should|pretty|simply|enough|gonna|going|wtf|so|such|will|you|your|they|like|more)\b/},COMMENT=function(e,n,t={}){e=inherit$1({scope:"comment",begin:e,end:n,contains:[]},t),e.contains.push({scope:"doctag",begin:"[ ]*(?=(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):)",end:/(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):/,excludeBegin:!0,relevance:0}),n=either("I","a","is","so","us","to","at","if","in","it","on",/[A-Za-z]+['](d|ve|re|ll|t|s|n)/,/[A-Za-z]+[-][a-z]+/,/[A-Za-z][a-z]{2,}/);return e.contains.push({begin:concat(/[ ]+/,"(",n,/[.]?[:]?([.][ ]|[ ])/,"){3}")}),e},C_LINE_COMMENT_MODE=COMMENT("//","$"),C_BLOCK_COMMENT_MODE=COMMENT("/\\*","\\*/"),HASH_COMMENT_MODE=COMMENT("#","$"),NUMBER_MODE={scope:"number",begin:NUMBER_RE,relevance:0},C_NUMBER_MODE={scope:"number",begin:C_NUMBER_RE,relevance:0},BINARY_NUMBER_MODE={scope:"number",begin:BINARY_NUMBER_RE,relevance:0},REGEXP_MODE={scope:"regexp",begin:/\/(?=[^/\n]*\/)/,end:/\/[gimuy]*/,contains:[BACKSLASH_ESCAPE,{begin:/\[/,end:/\]/,relevance:0,contains:[BACKSLASH_ESCAPE]}]},TITLE_MODE={scope:"title",begin:IDENT_RE,relevance:0},UNDERSCORE_TITLE_MODE={scope:"title",begin:UNDERSCORE_IDENT_RE,relevance:0},METHOD_GUARD={begin:"\\.\\s*"+UNDERSCORE_IDENT_RE,relevance:0},END_SAME_AS_BEGIN=function(e){return Object.assign(e,{"on:begin":(e,n)=>{n.data._beginMatch=e[1]},"on:end":(e,n)=>{n.data._beginMatch!==e[1]&&n.ignoreMatch()}})};var MODES=Object.freeze({__proto__:null,APOS_STRING_MODE:APOS_STRING_MODE,BACKSLASH_ESCAPE:BACKSLASH_ESCAPE,BINARY_NUMBER_MODE:BINARY_NUMBER_MODE,BINARY_NUMBER_RE:BINARY_NUMBER_RE,COMMENT:COMMENT,C_BLOCK_COMMENT_MODE:C_BLOCK_COMMENT_MODE,C_LINE_COMMENT_MODE:C_LINE_COMMENT_MODE,C_NUMBER_MODE:C_NUMBER_MODE,C_NUMBER_RE:C_NUMBER_RE,END_SAME_AS_BEGIN:END_SAME_AS_BEGIN,HASH_COMMENT_MODE:HASH_COMMENT_MODE,IDENT_RE:IDENT_RE,MATCH_NOTHING_RE:MATCH_NOTHING_RE,METHOD_GUARD:METHOD_GUARD,NUMBER_MODE:NUMBER_MODE,NUMBER_RE:NUMBER_RE,PHRASAL_WORDS_MODE:PHRASAL_WORDS_MODE,QUOTE_STRING_MODE:QUOTE_STRING_MODE,REGEXP_MODE:REGEXP_MODE,RE_STARTERS_RE:RE_STARTERS_RE,SHEBANG:SHEBANG,TITLE_MODE:TITLE_MODE,UNDERSCORE_IDENT_RE:UNDERSCORE_IDENT_RE,UNDERSCORE_TITLE_MODE:UNDERSCORE_TITLE_MODE});function skipIfHasPrecedingDot(e,n){"."===e.input[e.index-1]&&n.ignoreMatch()}function scopeClassName(e,n){void 0!==e.className&&(e.scope=e.className,delete e.className)}function beginKeywords(e,n){n&&e.beginKeywords&&(e.begin="\\b("+e.beginKeywords.split(" ").join("|")+")(?!\\.)(?=\\b|\\s)",e.__beforeBegin=skipIfHasPrecedingDot,e.keywords=e.keywords||e.beginKeywords,delete e.beginKeywords,void 0===e.relevance)&&(e.relevance=0)}function compileIllegal(e,n){Array.isArray(e.illegal)&&(e.illegal=either(...e.illegal))}function compileMatch(e,n){if(e.match){if(e.begin||e.end)throw new Error("begin & end are not supported with match");e.begin=e.match,delete e.match}}function compileRelevance(e,n){void 0===e.relevance&&(e.relevance=1)}let beforeMatchExt=(n,e)=>{if(n.beforeMatch){if(n.starts)throw new Error("beforeMatch cannot be used with starts");var t=Object.assign({},n);Object.keys(n).forEach(e=>{delete n[e]}),n.keywords=t.keywords,n.begin=concat(t.beforeMatch,lookahead(t.begin)),n.starts={relevance:0,contains:[Object.assign(t,{endsParent:!0})]},n.relevance=0,delete t.beforeMatch}},COMMON_KEYWORDS=["of","and","for","in","not","or","if","then","parent","list","value"],DEFAULT_KEYWORD_SCOPE="keyword";function compileKeywords(n,t,e=DEFAULT_KEYWORD_SCOPE){let i=Object.create(null);return"string"==typeof n?r(e,n.split(" ")):Array.isArray(n)?r(e,n):Object.keys(n).forEach(function(e){Object.assign(i,compileKeywords(n[e],t,e))}),i;function r(n,e){(e=t?e.map(e=>e.toLowerCase()):e).forEach(function(e){e=e.split("|");i[e[0]]=[n,scoreForKeyword(e[0],e[1])]})}}function scoreForKeyword(e,n){return n?Number(n):commonKeyword(e)?0:1}function commonKeyword(e){return COMMON_KEYWORDS.includes(e.toLowerCase())}let seenDeprecations={},error=e=>{console.error(e)},warn=(e,...n)=>{console.log("WARN: "+e,...n)},deprecated=(e,n)=>{seenDeprecations[e+"/"+n]||(console.log(`Deprecated as of ${e}. `+n),seenDeprecations[e+"/"+n]=!0)},MultiClassError=new Error;function remapScopeNames(e,n,{key:t}){let i=0;var r=e[t],o={},a={};for(let e=1;e<=n.length;e++)a[e+i]=r[e],o[e+i]=!0,i+=countMatchGroups(n[e-1]);e[t]=a,e[t]._emit=o,e[t]._multi=!0}function beginMultiClass(e){if(Array.isArray(e.begin)){if(e.skip||e.excludeBegin||e.returnBegin)throw error("skip, excludeBegin, returnBegin not compatible with beginScope: {}"),MultiClassError;if("object"!=typeof e.beginScope||null===e.beginScope)throw error("beginScope must be object"),MultiClassError;remapScopeNames(e,e.begin,{key:"beginScope"}),e.begin=_rewriteBackreferences(e.begin,{joinWith:""})}}function endMultiClass(e){if(Array.isArray(e.end)){if(e.skip||e.excludeEnd||e.returnEnd)throw error("skip, excludeEnd, returnEnd not compatible with endScope: {}"),MultiClassError;if("object"!=typeof e.endScope||null===e.endScope)throw error("endScope must be object"),MultiClassError;remapScopeNames(e,e.end,{key:"endScope"}),e.end=_rewriteBackreferences(e.end,{joinWith:""})}}function scopeSugar(e){e.scope&&"object"==typeof e.scope&&null!==e.scope&&(e.beginScope=e.scope,delete e.scope)}function MultiClass(e){scopeSugar(e),"string"==typeof e.beginScope&&(e.beginScope={_wrap:e.beginScope}),"string"==typeof e.endScope&&(e.endScope={_wrap:e.endScope}),beginMultiClass(e),endMultiClass(e)}function compileLanguage(o){function a(e,n){return new RegExp(source(e),"m"+(o.case_insensitive?"i":"")+(o.unicodeRegex?"u":"")+(n?"g":""))}class n{constructor(){this.matchIndexes={},this.regexes=[],this.matchAt=1,this.position=0}addRule(e,n){n.position=this.position++,this.matchIndexes[this.matchAt]=n,this.regexes.push([n,e]),this.matchAt+=countMatchGroups(e)+1}compile(){0===this.regexes.length&&(this.exec=()=>null);var e=this.regexes.map(e=>e[1]);this.matcherRe=a(_rewriteBackreferences(e,{joinWith:"|"}),!0),this.lastIndex=0}exec(e){this.matcherRe.lastIndex=this.lastIndex;var n,t,e=this.matcherRe.exec(e);return e?(n=e.findIndex((e,n)=>0<n&&void 0!==e),t=this.matchIndexes[n],e.splice(0,n),Object.assign(e,t)):null}}class s{constructor(){this.rules=[],this.multiRegexes=[],this.count=0,this.lastIndex=0,this.regexIndex=0}getMatcher(e){if(this.multiRegexes[e])return this.multiRegexes[e];let t=new n;return this.rules.slice(e).forEach(([e,n])=>t.addRule(e,n)),t.compile(),this.multiRegexes[e]=t}resumingScanAtSamePosition(){return 0!==this.regexIndex}considerAll(){this.regexIndex=0}addRule(e,n){this.rules.push([e,n]),"begin"===n.type&&this.count++}exec(e){var n=this.getMatcher(this.regexIndex);n.lastIndex=this.lastIndex;let t=n.exec(e);return!this.resumingScanAtSamePosition()||t&&t.index===this.lastIndex||((n=this.getMatcher(0)).lastIndex=this.lastIndex+1,t=n.exec(e)),t&&(this.regexIndex+=t.position+1,this.regexIndex===this.count)&&this.considerAll(),t}}if(o.compilerExtensions||(o.compilerExtensions=[]),o.contains&&o.contains.includes("self"))throw new Error("ERR: contains `self` is not supported at the top-level of a language.  See documentation.");return o.classNameAliases=inherit$1(o.classNameAliases||{}),function n(t,i){let r=t;if(!t.isCompiled){[scopeClassName,compileMatch,MultiClass,beforeMatchExt].forEach(e=>e(t,i)),o.compilerExtensions.forEach(e=>e(t,i)),t.__beforeBegin=null,[beginKeywords,compileIllegal,compileRelevance].forEach(e=>e(t,i)),t.isCompiled=!0;let e=null;"object"==typeof t.keywords&&t.keywords.$pattern&&(t.keywords=Object.assign({},t.keywords),e=t.keywords.$pattern,delete t.keywords.$pattern),e=e||/\w+/,t.keywords&&(t.keywords=compileKeywords(t.keywords,o.case_insensitive)),r.keywordPatternRe=a(e,!0),i&&(t.begin||(t.begin=/\B|\b/),r.beginRe=a(r.begin),t.end||t.endsWithParent||(t.end=/\B|\b/),t.end&&(r.endRe=a(r.end)),r.terminatorEnd=source(r.end)||"",t.endsWithParent)&&i.terminatorEnd&&(r.terminatorEnd+=(t.end?"|":"")+i.terminatorEnd),t.illegal&&(r.illegalRe=a(t.illegal)),t.contains||(t.contains=[]),t.contains=[].concat(...t.contains.map(function(e){return expandOrCloneMode("self"===e?t:e)})),t.contains.forEach(function(e){n(e,r)}),t.starts&&n(t.starts,i),r.matcher=function(e){let n=new s;return e.contains.forEach(e=>n.addRule(e.begin,{rule:e,type:"begin"})),e.terminatorEnd&&n.addRule(e.terminatorEnd,{type:"end"}),e.illegal&&n.addRule(e.illegal,{type:"illegal"}),n}(r)}return r}(o)}function dependencyOnParent(e){return!!e&&(e.endsWithParent||dependencyOnParent(e.starts))}function expandOrCloneMode(n){return n.variants&&!n.cachedVariants&&(n.cachedVariants=n.variants.map(function(e){return inherit$1(n,{variants:null},e)})),n.cachedVariants||(dependencyOnParent(n)?inherit$1(n,{starts:n.starts?inherit$1(n.starts):null}):Object.isFrozen(n)?inherit$1(n):n)}var version="11.9.0";class HTMLInjectionError extends Error{constructor(e,n){super(e),this.name="HTMLInjectionError",this.html=n}}let escape=escapeHTML,inherit=inherit$1,NO_MATCH=Symbol("nomatch"),MAX_KEYWORD_HITS=7,HLJS=function(i){let y=Object.create(null),a=Object.create(null),r=[],A=!0,v="Could not find the language '{}', did you forget to load/include a language module?",o={disableAutodetect:!0,name:"Plain text",contains:[]},C={ignoreUnescapedHTML:!1,throwUnescapedHTML:!1,noHighlightRe:/^(no-?highlight)$/i,languageDetectRe:/\blang(?:uage)?-([\w-]+)\b/i,classPrefix:"hljs-",cssSelector:"pre code",languages:null,__emitter:TokenTreeEmitter};function s(e){return C.noHighlightRe.test(e)}function l(e,n,t){let i="",r="";"object"==typeof n?(i=e,t=n.ignoreIllegals,r=n.language):(deprecated("10.7.0","highlight(lang, code, ...args) has been deprecated."),deprecated("10.7.0","Please use highlight(code, options) instead.\nhttps://github.com/highlightjs/highlight.js/issues/2277"),r=e,i=n),void 0===t&&(t=!0);e={code:i,language:r},u("before:highlight",e),n=e.result||D(e.language,e.code,t);return n.code=e.code,u("after:highlight",n),n}function D(i,r,o,e){let a=Object.create(null);function s(){if(_.keywords){let e=0,n=(_.keywordPatternRe.lastIndex=0,_.keywordPatternRe.exec(O)),t="";for(;n;){t+=O.substring(e,n.index);var i,r=E.case_insensitive?n[0].toLowerCase():n[0],o=_.keywords[r];!o||([o,i]=o,m.addText(t),t="",a[r]=(a[r]||0)+1,a[r]<=MAX_KEYWORD_HITS&&(R+=i),o.startsWith("_"))?t+=n[0]:(r=E.classNameAliases[o]||o,c(n[0],r)),e=_.keywordPatternRe.lastIndex,n=_.keywordPatternRe.exec(O)}t+=O.substring(e),m.addText(t)}else m.addText(O)}function l(){(null!=_.subLanguage?function(){if(""!==O){let e=null;if("string"==typeof _.subLanguage){if(!y[_.subLanguage])return m.addText(O);e=D(_.subLanguage,O,!0,b[_.subLanguage]),b[_.subLanguage]=e._top}else e=I(O,_.subLanguage.length?_.subLanguage:null);0<_.relevance&&(R+=e.relevance),m.__addSublanguage(e._emitter,e.language)}}:s)(),O=""}function c(e,n){""!==e&&(m.startScope(n),m.addText(e),m.endScope())}function g(e,n){let t=1;for(var i,r,o=n.length-1;t<=o;)e._emit[t]&&(i=E.classNameAliases[e[t]]||e[t],r=n[t],i?c(r,i):(O=r,s(),O="")),t++}function u(e,n){e.scope&&"string"==typeof e.scope&&m.openNode(E.classNameAliases[e.scope]||e.scope),e.beginScope&&(e.beginScope._wrap?(c(O,E.classNameAliases[e.beginScope._wrap]||e.beginScope._wrap),O=""):e.beginScope._multi&&(g(e.beginScope,n),O="")),_=Object.create(e,{parent:{value:_}})}function d(e){var n,t,i=e[0],r=e.rule,o=new Response(r);for(n of[r.__beforeBegin,r["on:begin"]])if(n&&(n(e,o),o.isMatchIgnored))return t=i,0===_.matcher.regexIndex?(O+=t[0],1):(w=!0,0);return r.skip?O+=i:(r.excludeBegin&&(O+=i),l(),r.returnBegin||r.excludeBegin||(O=i)),u(r,e),r.returnBegin?0:i.length}function h(e){var n=e[0],t=r.substring(e.index),i=function e(n,t,i){let r=startsWith(n.endRe,i);var o;if(r=r&&(!n["on:end"]||(o=new Response(n),n["on:end"](t,o),!o.isMatchIgnored))&&r){for(;n.endsParent&&n.parent;)n=n.parent;return n}if(n.endsWithParent)return e(n.parent,t,i)}(_,e,t);if(!i)return NO_MATCH;t=_;for(_.endScope&&_.endScope._wrap?(l(),c(n,_.endScope._wrap)):_.endScope&&_.endScope._multi?(l(),g(_.endScope,e)):t.skip?O+=n:(t.returnEnd||t.excludeEnd||(O+=n),l(),t.excludeEnd&&(O=n));_.scope&&m.closeNode(),_.skip||_.subLanguage||(R+=_.relevance),(_=_.parent)!==i.parent;);return i.starts&&u(i.starts,e),t.returnEnd?0:n.length}let p={};function n(e,n){var t=n&&n[0];if(O+=e,null==t)return l(),0;if("begin"===p.type&&"end"===n.type&&p.index===n.index&&""===t){if(O+=r.slice(n.index,n.index+1),A)return 1;throw(e=new Error(`0 width match regex (${i})`)).languageName=i,e.badRule=p.rule,e}if("begin"===(p=n).type)return d(n);if("illegal"===n.type&&!o)throw(e=new Error('Illegal lexeme "'+t+'" for mode "'+(_.scope||"<unnamed>")+'"')).mode=_,e;if("end"===n.type){e=h(n);if(e!==NO_MATCH)return e}if("illegal"===n.type&&""===t)return 1;if(1e5<S&&S>3*n.index)throw new Error("potential infinite loop, way more iterations than matches");return O+=t,t.length}let E=k(i);if(!E)throw error(v.replace("{}",i)),new Error('Unknown language: "'+i+'"');var t=compileLanguage(E);let f="",_=e||t,b={},m=new C.__emitter(C);var M=[];for(let e=_;e!==E;e=e.parent)e.scope&&M.unshift(e.scope);M.forEach(e=>m.openNode(e));let O="",R=0,N=0,S=0,w=!1;try{if(E.__emitTokens)E.__emitTokens(r,m);else{for(_.matcher.considerAll();;){S++,w?w=!1:_.matcher.considerAll(),_.matcher.lastIndex=N;var T=_.matcher.exec(r);if(!T)break;var x=n(r.substring(N,T.index),T);N=T.index+x}n(r.substring(N))}return m.finalize(),f=m.toHTML(),{language:i,value:f,relevance:R,illegal:!1,_emitter:m,_top:_}}catch(e){if(e.message&&e.message.includes("Illegal"))return{language:i,value:escape(r),illegal:!0,relevance:0,_illegalBy:{message:e.message,index:N,context:r.slice(N-100,N+100),mode:e.mode,resultSoFar:f},_emitter:m};if(A)return{language:i,value:escape(r),illegal:!1,relevance:0,errorRaised:e,_emitter:m,_top:_};throw e}}function I(n,e){e=e||C.languages||Object.keys(y);t=n,(i={value:escape(t),illegal:!1,relevance:0,_top:o,_emitter:new C.__emitter(C)})._emitter.addText(t);var t=i,i=e.filter(k).filter(g).map(e=>D(e,n,!1)),e=(i.unshift(t),i.sort((e,n)=>{if(e.relevance!==n.relevance)return n.relevance-e.relevance;if(e.language&&n.language){if(k(e.language).supersetOf===n.language)return 1;if(k(n.language).supersetOf===e.language)return-1}return 0})),[t,i]=e,e=t;return e.secondBest=i,e}function n(e){t=(n=e).className+" ",t+=n.parentNode?n.parentNode.className:"";var n=(o=C.languageDetectRe.exec(t))?((r=k(o[1]))||(warn(v.replace("{}",o[1])),warn("Falling back to no-highlight mode for this block.",n)),r?o[1]:"no-highlight"):t.split(/\s+/).find(e=>s(e)||k(e));if(!s(n))if(u("before:highlightElement",{el:e,language:n}),e.dataset.highlighted)console.log("Element previously highlighted. To highlight again, first unset `dataset.highlighted`.",e);else{if(0<e.children.length)if(C.ignoreUnescapedHTML||(console.warn("One of your code blocks includes unescaped HTML. This is a potentially serious security risk."),console.warn("https://github.com/highlightjs/highlight.js/wiki/security"),console.warn("The element with unescaped HTML:"),console.warn(e)),C.throwUnescapedHTML)throw new HTMLInjectionError("One of your code blocks includes unescaped HTML.",e.innerHTML);var t,i,r=e.textContent,o=n?l(r,{language:n,ignoreIllegals:!0}):I(r);e.innerHTML=o.value,e.dataset.highlighted="yes",t=e,i=o.language,n=(n=n)&&a[n]||i,t.classList.add("hljs"),t.classList.add("language-"+n),e.result={language:o.language,re:o.relevance,relevance:o.relevance},o.secondBest&&(e.secondBest={language:o.secondBest.language,relevance:o.secondBest.relevance}),u("after:highlightElement",{el:e,result:o,text:r})}}let e=!1;function t(){"loading"===document.readyState?e=!0:document.querySelectorAll(C.cssSelector).forEach(n)}function k(e){return e=(e||"").toLowerCase(),y[e]||y[a[e]]}function c(e,{languageName:n}){(e="string"==typeof e?[e]:e).forEach(e=>{a[e.toLowerCase()]=n})}function g(e){e=k(e);return e&&!e.disableAutodetect}function u(e,n){let t=e;r.forEach(function(e){e[t]&&e[t](n)})}for(var d in"undefined"!=typeof window&&window.addEventListener&&window.addEventListener("DOMContentLoaded",function(){e&&t()},!1),Object.assign(i,{highlight:l,highlightAuto:I,highlightAll:t,highlightElement:n,highlightBlock:function(e){return deprecated("10.7.0","highlightBlock will be removed entirely in v12.0"),deprecated("10.7.0","Please use highlightElement now."),n(e)},configure:function(e){C=inherit(C,e)},initHighlighting:()=>{t(),deprecated("10.6.0","initHighlighting() deprecated.  Use highlightAll() now.")},initHighlightingOnLoad:function(){t(),deprecated("10.6.0","initHighlightingOnLoad() deprecated.  Use highlightAll() now.")},registerLanguage:function(n,e){let t=null;try{t=e(i)}catch(e){if(error("Language definition for '{}' could not be registered.".replace("{}",n)),!A)throw e;error(e),t=o}t.name||(t.name=n),(y[n]=t).rawDefinition=e.bind(null,i),t.aliases&&c(t.aliases,{languageName:n})},unregisterLanguage:function(e){delete y[e];for(var n of Object.keys(a))a[n]===e&&delete a[n]},listLanguages:function(){return Object.keys(y)},getLanguage:k,registerAliases:c,autoDetection:g,inherit:inherit,addPlugin:function(e){var n;(n=e)["before:highlightBlock"]&&!n["before:highlightElement"]&&(n["before:highlightElement"]=e=>{n["before:highlightBlock"](Object.assign({block:e.el},e))}),n["after:highlightBlock"]&&!n["after:highlightElement"]&&(n["after:highlightElement"]=e=>{n["after:highlightBlock"](Object.assign({block:e.el},e))}),r.push(e)},removePlugin:function(e){-1!==(e=r.indexOf(e))&&r.splice(e,1)}}),i.debugMode=function(){A=!1},i.safeMode=function(){A=!0},i.versionString=version,i.regex={concat:concat,lookahead:lookahead,either:either,optional:optional,anyNumberOfTimes:anyNumberOfTimes},MODES)"object"==typeof MODES[d]&&deepFreeze(MODES[d]);return Object.assign(i,MODES),i},highlight=HLJS({});highlight.newInstance=()=>HLJS({}),(highlight.HighlightJS=highlight).default=highlight;export default function(){return highlight}