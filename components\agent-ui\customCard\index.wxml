<!--components/agent-ui-new/chatFIle/chatFile.wxml-->
<!-- <text>components/agent-ui-new/chatFIle/chatFile.wxml</text> -->
<!-- <block>
  <view wx:if="{{name === 'maps_geo' || name === 'maps_direction_driving'}}">
    <custom-map name="{{name}}" toolData="{{toolData}}"></custom-map>
  </view>
  <view wx:if="{{name === 'maps_weather'}}">
    <custom-weather name="{{name}}" toolData="{{toolData}}"></custom-weather>
  </view>
  <view wx:if="{{name === 'map_search_places'}}">
    <custom-business-list name="{{name}}" toolData="{{toolData}}"></custom-business-list>
    <custom-food-list name="{{name}}" toolData="{{toolData}}"></custom-food-list>
  </view>
</block> -->

<block>
  <view class="customCard">
    <custom-map wx:if="{{name === 'geocoder' || name === 'placeSearchNearby' || 'directionDriving'}}" name="{{name}}" toolParams="{{toolParams}}" toolData="{{toolData}}"></custom-map>
  </view>
  <view class="customCard">
    <custom-weather  wx:if="{{name === 'weather'}}" name="{{name}}" toolData="{{toolData}}"></custom-weather>
  </view>
  <view class="customCard" wx:if="{{name === 'placeSearchNearby'}}">
    <custom-business-list name="{{name}}" toolData="{{toolData}}"></custom-business-list>
  </view>
  <!-- 用户可类似添加自定义组件 -->
</block>