.route-list {
  display: flex;
  flex-direction: column;
  max-height: 400px;
  background-color: #fff;
  padding-right: 20px;
  border-radius: 8px;
}

.route-header {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1px solid #eee;
}

.back-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
}

.route-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.route-content {
  flex: 1;
  overflow-y: scroll;
  padding: 20rpx;
}

.route-item {
  display: flex;
  align-items: flex-start;
  padding: 20rpx 0;
  border-bottom: 1px solid #f5f5f5;
}

.route-item:last-child {
  border-bottom: none;
}

.icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
}

.details {
  display: flex;
  flex-direction: column;
}

.instruction {
  font-size: 28rpx;
  color: #333;
}

.direction {
  font-size: 24rpx;
  color: #999;
  margin-top: 4rpx;
}

.start .icon {
  background-color: #07c160;
  border-radius: 50%;
  color: white;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.end .icon {
  background-color: #DE544e;
  border-radius: 50%;
  color: white;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}